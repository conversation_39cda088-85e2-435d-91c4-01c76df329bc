from .celery_app import celery_app
from .utils.email_utils import send_report_email, send_error_notification_email
import logging
from datetime import datetime
import traceback
import pandas as pd

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Task configuration mapping
TASK_CONFIGS = {
    'daily': {
        'description': 'Daily case fetch',
        'fetch_params': {'nb_days': 1},
        'email_subject': 'Daily Case Fetch'
    },
    'weekly': {
        'description': 'Weekly case fetch (last week)',
        'fetch_params': {'period_type': 'weekly'},
        'email_subject': 'Weekly Case Fetch'
    },
    'monthly_1': {
        'description': 'Monthly case fetch (last month)',
        'fetch_params': {'period_type': 'monthly_1'},
        'email_subject': 'Monthly Case Fetch (Last Month)'
    },
    'monthly_2': {
        'description': 'Monthly case fetch (2 months ago)',
        'fetch_params': {'period_type': 'monthly_2'},
        'email_subject': 'Monthly Case Fetch (2 Months Ago)'
    },
    'monthly_3': {
        'description': 'Monthly case fetch (3 months ago)',
        'fetch_params': {'period_type': 'monthly_3'},
        'email_subject': 'Monthly Case Fetch (3 Months Ago)'
    },
    'monthly_4': {
        'description': 'Monthly case fetch (open cases 3-18 months old)',
        'fetch_params': {'period_type': 'monthly_4'},
        'email_subject': 'Monthly Case Fetch (Open Cases 3-18 Months Old)'
    }
}

@celery_app.task(bind=True, name='unified_case_fetch_task')
def unified_case_fetch_task(self, task_type, **kwargs):
    """
    Unified task to handle all case fetch operations
    
    Args:
        task_type: One of 'daily', 'weekly', 'monthly_1', 'monthly_2', 'monthly_3', 'monthly_4'
        **kwargs: Additional parameters (e.g., num_days for daily tasks)
    """
    try:
        # Get task configuration
        if task_type not in TASK_CONFIGS:
            raise ValueError(f"Unknown task_type: {task_type}. Valid types: {list(TASK_CONFIGS.keys())}")
        
        config = TASK_CONFIGS[task_type]
        description = config['description']
        
        logger.info(f"Starting {description}")
        self.update_state(state='PROGRESS', meta={'status': f'Starting {description}...'})

        # Import the unified fetch function
        from Alerts.WorkflowManager import run_case_fetch
        
        # Prepare fetch parameters
        fetch_params = config['fetch_params'].copy()
        
        # Override with any provided kwargs (useful for dynamic daily tasks)
        if task_type == 'daily' and 'num_days' in kwargs:
            fetch_params['nb_days'] = kwargs['num_days']
        
        # Update progress
        self.update_state(state='PROGRESS', meta={'status': f'Fetching cases for {description}...'})
        
        # Execute the fetch
        combined_cases_df, tracking_dict = run_case_fetch(**fetch_params)
        
        # Get statistics
        stats = get_case_statistics(combined_cases_df, tracking_dict)
        stats['fetch_type'] = task_type
        
        # Send email report
        self.update_state(state='PROGRESS', meta={'status': f'Sending {description} email report...'})
        send_report_email(stats)
        
        logger.info(f"{description} completed successfully")
        
        return {
            'status': 'completed',
            'task_type': task_type,
            'timestamp': datetime.now().isoformat(),
            'stats': stats,
            'message': f'{description} completed successfully'
        }
        
    except Exception as exc:
        error_msg = f"{description} failed: {str(exc)}"
        logger.error(error_msg)
        logger.error(traceback.format_exc())
        
        # Send error notification
        send_error_notification_email(str(exc), config['email_subject'])
        
        self.update_state(
            state='FAILURE',
            meta={
                'error': str(exc),
                'traceback': traceback.format_exc(),
                'task_type': task_type
            }
        )
        raise exc


@celery_app.task(bind=True, name='daily_case_fetch_task')
def daily_case_fetch_task(self, num_days=1):
    return unified_case_fetch_task.apply_async(
        args=['daily'],
        kwargs={'num_days': num_days},
        task_id=self.request.id
    )

@celery_app.task(bind=True, name='weekly_case_fetch_task')
def weekly_case_fetch_task(self):
    return unified_case_fetch_task.apply_async(
        args=['weekly'],
        task_id=self.request.id
    )

@celery_app.task(bind=True, name='monthly_case_fetch_1_task')
def monthly_case_fetch_1_task(self):
    return unified_case_fetch_task.apply_async(
        args=['monthly_1'],
        task_id=self.request.id
    )

@celery_app.task(bind=True, name='monthly_case_fetch_2_task')
def monthly_case_fetch_2_task(self):
    return unified_case_fetch_task.apply_async(
        args=['monthly_2'],
        task_id=self.request.id
    )

@celery_app.task(bind=True, name='monthly_case_fetch_3_task')
def monthly_case_fetch_3_task(self):
    return unified_case_fetch_task.apply_async(
        args=['monthly_3'],
        task_id=self.request.id
    )

@celery_app.task(bind=True, name='monthly_case_fetch_4_task')
def monthly_case_fetch_4_task(self):

    return unified_case_fetch_task.apply_async(
        args=['monthly_4'],
        task_id=self.request.id
    )

def get_case_statistics(combined_cases_df, tracking_dict):
    """
    Extract statistics from the fetch result and IP tracking data
    """
    try:
        total_cases = len(combined_cases_df) if isinstance(combined_cases_df, pd.DataFrame) else 0
        fetch_type = tracking_dict.get('fetch_type', 'daily')

        if total_cases == 0:
            logger.info("No cases found - returning empty statistics")
            return {
                'total_cases_processed': 0,
                'fetch_type': fetch_type,
                'ip_statistics': {
                    'cases_no_ip_gained_some': 0,
                    'cases_no_ip_gained_all': 0,
                    'cases_some_ip_gained_more': 0,
                    'cases_some_ip_gained_all': 0,
                    'cases_ip_regressed': 0,
                    'cases_already_complete': 0,
                    'total_processed': 0
                },
                'enhanced_statistics': {
                    'new_cases_count': 0,
                    'title_changes_count': 0,
                    'closed_cases_count': 0,
                    'cases_with_more_steps': 0,
                    'cases_missing_ip_before': 0,
                    'cases_missing_ip_after': 0,
                    'cases_gained_ip': 0
                },
                'fetch_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'case_details': [],
                'no_cases_found': True
            }

        def safe_count(data):
            if isinstance(data, list):
                return len(data)
            elif isinstance(data, (int, float)):
                return int(data)
            else:
                return 0

        ip_stats = {
            'cases_no_ip_gained_some': safe_count(tracking_dict.get('cases_no_ip_then_some_ip', 0)),
            'cases_no_ip_gained_all': safe_count(tracking_dict.get('cases_no_ip_then_all_ip', 0)),
            'cases_some_ip_gained_more': safe_count(tracking_dict.get('cases_some_ip_then_more_ip', 0)),
            'cases_some_ip_gained_all': safe_count(tracking_dict.get('cases_some_ip_then_all_ip', 0)),
            'cases_ip_regressed': safe_count(tracking_dict.get('cases_ip_regressed', 0)),
            'cases_already_complete': safe_count(tracking_dict.get('cases_already_all_ip', 0)),
            'total_processed': tracking_dict.get('total_count', 0)
        }

        enhanced_stats = {
            'new_cases_count': tracking_dict.get('new_cases_count', 0),
            'title_changes_count': tracking_dict.get('title_changes_count', 0),
            'closed_cases_count': tracking_dict.get('closed_cases_count', 0),
            'cases_with_more_steps': tracking_dict.get('cases_with_more_steps', 0),
            'cases_missing_ip_before': tracking_dict.get('cases_missing_ip_before', 0),
            'cases_missing_ip_after': tracking_dict.get('cases_missing_ip_after', 0),
            'cases_gained_ip': tracking_dict.get('cases_gained_ip', 0)
        }

        return {
            'total_cases_processed': total_cases,
            'fetch_type': fetch_type,
            'ip_statistics': ip_stats,
            'enhanced_statistics': enhanced_stats,
            'fetch_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'case_details': combined_cases_df.to_dict('records') if isinstance(combined_cases_df, pd.DataFrame) and not combined_cases_df.empty else [],
            'no_cases_found': False,
            'success_count': tracking_dict.get('success_count', 0)
        }

    except Exception as e:
        logger.error(f"Error getting case statistics: {str(e)}")
        return {
            'total_cases_processed': 0,
            'fetch_type': 'unknown',
            'ip_statistics': {
                'cases_no_ip_gained_some': 0,
                'cases_no_ip_gained_all': 0,
                'cases_some_ip_gained_more': 0,
                'cases_some_ip_gained_all': 0,
                'cases_ip_regressed': 0,
                'cases_already_complete': 0,
                'total_processed': 0
            },
            'enhanced_statistics': {
                'new_cases_count': 0,
                'title_changes_count': 0,
                'closed_cases_count': 0,
                'cases_with_more_steps': 0,
                'cases_missing_ip_before': 0,
                'cases_missing_ip_after': 0,
                'cases_gained_ip': 0
            },
            'fetch_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'case_details': [],
            'error': str(e),
            'no_cases_found': True
        }

@celery_app.task
def test_task():
    """Simple test task to verify Celery is working"""
    logger.info("Test task executed successfully")
    return "Test task completed successfully!"

# Task monitoring signals
from celery.signals import task_prerun, task_postrun, task_failure

@task_prerun.connect
def task_prerun_handler(sender=None, task_id=None, task=None, args=None, kwargs=None, **kwds):
    logger.info(f'Task {task.name} started: {task_id}')

@task_postrun.connect
def task_postrun_handler(sender=None, task_id=None, task=None, args=None, kwargs=None, retval=None, state=None, **kwds):
    logger.info(f'Task {task.name} completed: {task_id} - State: {state}')

@task_failure.connect
def task_failure_handler(sender=None, task_id=None, exception=None, traceback=None, einfo=None, **kwds):
    logger.error(f'Task {sender.name} failed: {task_id} - Exception: {exception}')