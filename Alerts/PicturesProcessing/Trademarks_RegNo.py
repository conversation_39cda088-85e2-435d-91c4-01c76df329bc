import os, sys, os, time, asyncio, shutil, time
from typing import Any

sys.path.append(os.getcwd())

import pandas as pd
from logdata import log_message
from Alerts.PicturesProcessing.RemoveDuplicates import remove_exact_duplicates, remove_near_duplicates, remove_text_duplicates
from IP.Trademarks.Trademark_API import get_trademarks_uspto, get_certificate_local_path
from Common import Constants
from langfuse import observe
import langfuse
from Alerts.IPTrackingManager import IPTrackingManager # Added import

@observe(capture_input=False, capture_output=False)
async def process_trademark_regno(df, index, case_images_directory, nums, ip_manager: IPTrackingManager, source: str = "Unknown"):
    """
    Processes trademark registration numbers found (e.g., by LLM) by fetching data from USPTO.

    Args:
        df: DataFrame containing case data.
        index: Index of the current case in the DataFrame.
        case_images_directory: Directory to save extracted images.
        nums: List of registration numbers or serial numbers to process.
        ip_manager: Instance of IPTrackingManager to track IP processing state.
        source: Description of where these registration numbers came from.
    """
    # Get the plaintiff_id from the dataframe
    plaintiff_id = df.at[index, "plaintiff_id"]
    
    # Prepare the list of trademarks to fetch from USPTO
    trademark_list = []
    for num in nums:
        full_filename = Constants.sanitize_name(df["docket"].loc[index]) + "_regno_" + str(num) + "_full.webp"
        trademark_filename = Constants.sanitize_name(df["docket"].loc[index]) + "_regno_" + str(num) + ".webp"

        metadata = {'full_filename': full_filename, 'trademark_filename': trademark_filename}
        if len(str(num)) == 8:
            trademark_list.append({"reg_no": None, "ser_no": num, "metadata": metadata, "plaintiff_id": plaintiff_id})
        else:
            trademark_list.append({"reg_no": num, "ser_no": None, "metadata": metadata, "plaintiff_id": plaintiff_id})
    
    # Get trademark data asynchronously
    start_time = time.time()
    df_trademarks = await get_trademarks_uspto(trademark_list)
    print(f"\033[91m 🔥 process_trademark_regno: USPTO Trademark API time: {len(df_trademarks)} in {format(time.time() - start_time, '.1f')} seconds\033[0m")

    # Store initial state for comparison
    initial_found_count = len(ip_manager.get_found_reg_nos('trademark'))

    # Process the returned trademark data
    processed_count = 0
    found_reg_nos = []
    for _, row in df_trademarks.iterrows():
        if "metadata" not in row:
            continue
            
        metadata = row["metadata"]
        formatted_reg_no = row["reg_no"]

        # Pass ip_manager down, state update happens within process_trademark_data
        if process_trademark_data(df, index, case_images_directory, row, metadata, 'byregno', ip_manager):
            processed_count += 1 # Increment only if successfully processed
            found_reg_nos.append(formatted_reg_no)

    # Optional: Log how many reg numbers were successfully processed
    log_message(f"- Processed {processed_count}/{len(nums)} trademarks found by LLM.")

    # Update Langfuse with batch results
    final_found_count = len(ip_manager.get_found_reg_nos('trademark'))
    langfuse.get_client().update_current_span(
        input={
            "CaseIndex": index,
            "NumRegNosToProcess": len(nums),
            "Source": source,
            "RegNosToProcess": nums
        },
        output={
            "TrademarksFromUSPTO": len(df_trademarks),
            "ProcessedCount": processed_count,
            "FoundRegNos": found_reg_nos,
            "NewlyFoundCount": final_found_count - initial_found_count,
            "TrademarkGoalMetAfter": ip_manager.is_goal_met('trademark')
        }
    )


@observe(capture_input=False, capture_output=False)
def deduplicate_all_trademarks(df: 'pd.DataFrame', index: Any, case_images_directory: str) -> None:
    """
    Removes duplicate trademarks (text, exact, near) from the case's trademark list
    and merges metadata from duplicates into the kept images.
    Operates on df.at[index, 'images']['trademarks'].
    """
    
    # Check if 'trademarks' key exists and is not empty
    trademarks_data = df.at[index, 'images'].get('trademarks')
    langfuse.get_client().update_current_span(
        input={
            "CaseIndex": index,
            "TrademarkCountBeforeDedup": len(trademarks_data) if trademarks_data else 0,
            "TrademarksDataInDF": trademarks_data
        }
    )
    if not trademarks_data: # Handles both missing 'trademarks' key or empty dict
        return

    log_message(f"        Deduplicating all trademarks for case index {index}...", level='INFO')

    initial_tm_count = len(trademarks_data)

    # This logic is moved from process_trademark_exhibit
    # It assumes df.at[index, 'images']['trademarks'] is the dictionary to work on.
    list_of_images_removed = remove_text_duplicates(case_images_directory, df.at[index, 'images']['trademarks'])
    
    current_images = list(df.at[index, 'images']['trademarks'].keys())
    # The following assumes remove_exact_duplicates and remove_near_duplicates return lists
    # of (removed_key, kept_key_or_None) tuples, or that the loop can handle mixed types.
    # This directly ports the original structure.
    if current_images: # Only proceed if there are images left
        list_of_images_removed += remove_exact_duplicates(case_images_directory, current_images)
    
    current_images = list(df.at[index, 'images']['trademarks'].keys())
    if current_images: # Only proceed if there are images left
        list_of_images_removed += remove_near_duplicates(case_images_directory, current_images, 12, 24)

    # Merge the qualitative info of the duplicates:
    for (image_filename, original_file) in list_of_images_removed:
        # Check if both keys exist before attempting merge
        # Also ensure original_file is not None if it's used as a key
        if image_filename in df.at[index, 'images']['trademarks'] and \
           original_file and original_file in df.at[index, 'images']['trademarks']:
            
            # Safely merge 'reg_no'
            reg_no_to_merge = df.at[index, 'images']['trademarks'][image_filename].get('reg_no', [])
            if reg_no_to_merge:
                existing_reg_no = df.at[index, 'images']['trademarks'][original_file].setdefault('reg_no', [])
                existing_reg_no.extend([item for item in reg_no_to_merge if item not in existing_reg_no])

            # Safely merge 'int_cls_list'
            int_cls_to_merge = df.at[index, 'images']['trademarks'][image_filename].get('int_cls_list', [])
            if int_cls_to_merge:
                existing_int_cls = df.at[index, 'images']['trademarks'][original_file].setdefault('int_cls_list', [])
                existing_int_cls.extend([item for item in int_cls_to_merge if item not in existing_int_cls])

            # Safely merge 'trademark_text'
            text_to_merge = df.at[index, 'images']['trademarks'][image_filename].get('trademark_text', [])
            if text_to_merge:
                existing_text = df.at[index, 'images']['trademarks'][original_file].setdefault('trademark_text', [])
                existing_text.extend([item for item in text_to_merge if item not in existing_text])

            # Safely merge 'full_filename'
            full_filename_to_merge = df.at[index, 'images']['trademarks'][image_filename].get('full_filename', [])
            if full_filename_to_merge:
                existing_full_filename = df.at[index, 'images']['trademarks'][original_file].setdefault('full_filename', [])
                existing_full_filename.extend([item for item in full_filename_to_merge if item not in existing_full_filename])

            # Remove the duplicate entry
            df.at[index, 'images']['trademarks'].pop(image_filename)
        elif image_filename in df.at[index, 'images']['trademarks']:
             # This case handles if original_file is None (e.g., for exact/near duplicates that just remove)
             # or if original_file was a key that got removed previously.
             log_message(f"        Removing duplicate image '{image_filename}' (original_file: {original_file} not suitable for merge target).", level='DEBUG')
             df.at[index, 'images']['trademarks'].pop(image_filename)

    final_tm_count = len(df.at[index, 'images'].get('trademarks', {})) # Recalculate safely
    log_message(f"        Trademark deduplication complete for case index {index}. "
                f"Initial count: {initial_tm_count}, Final count: {final_tm_count}.", level='INFO')

    langfuse.get_client().update_current_span(
        output={
            "FinalTMCount": len(df.at[index, 'images'].get('trademarks', {})) if index in df.index and 'images' in df.columns and isinstance(df.at[index, 'images'], dict) else 'N/A',
            "ImagesRemovedCount": len(list_of_images_removed) if 'list_of_images_removed' in locals() else 0,
            "FinalTrademarksData": df.at[index, 'images'].get('trademarks', {}) if index in df.index and 'images' in df.columns and isinstance(df.at[index, 'images'], dict) else 'N/A'
        }
    )


def process_trademark_data(df, index, case_images_directory, trademark_row, metadata, source_type, ip_manager: IPTrackingManager):
    """
    Helper function to process individual trademark data, copy images, update the dataframe, and update tracking state using IPTrackingManager.

    Args:
        df: DataFrame containing case data.
        index: Index of the current case in the DataFrame.
        case_images_directory: Directory to save extracted images.
        trademark_row: A row/dict containing trademark data from USPTO lookup.
        metadata: Dictionary containing filenames and potentially other info.
        source_type: String indicating how the trademark was found ('exhibit', 'byregno', 'byname').
        ip_manager: Instance of IPTrackingManager to track IP processing state.

    Returns:
        bool: True if trademark data was successfully processed and images copied, False otherwise.
    """
    formatted_reg_no = trademark_row.get("reg_no", "")
    if not formatted_reg_no:
        # Log if reg_no is missing, might indicate an issue with USPTO data for a serial number
        ser_no = trademark_row.get("ser_no")
        if ser_no:
            log_message(f"        - Warning: No registration number found for serial number {ser_no} in process_trademark_data.")
        return False
        
    # Get paths to trademark and certificate images
    tradeMark_img_path = os.path.join(Constants.local_ip_folder, "Trademarks", "Images", f"{formatted_reg_no}.webp")
    # Use new certificate path structure
    ser_no = trademark_row.get("ser_no")
    certificate_base_folder = os.path.join(Constants.local_ip_folder, "Trademarks", "Certificates")
    certificate_img_path = get_certificate_local_path(certificate_base_folder, ser_no=ser_no, reg_no=formatted_reg_no)
    
    # Extract metadata from the trademark
    trademark_text = trademark_row.get("text", "")
    int_cls_list = trademark_row.get("int_cls", [])
    
    # Copy images to the case directory if they exist
    images_copied = False
    if os.path.exists(tradeMark_img_path):
        shutil.copy(tradeMark_img_path, os.path.join(case_images_directory, metadata['trademark_filename']))
        images_copied = True
    else:
        print(f"\033[91m 🔥 Could not find trademark image: {formatted_reg_no}\033[0m")
        
    if os.path.exists(certificate_img_path) and 'full_filename' in metadata:
        shutil.copy(certificate_img_path, os.path.join(case_images_directory, metadata['full_filename']))
        
    # Update the dataframe if images were copied
    if images_copied:
        df.at[index, 'images']['trademarks'][metadata['trademark_filename']] = {
            'reg_no': [formatted_reg_no],
            'int_cls_list': int_cls_list,
            'trademark_text': [trademark_text],
            'full_filename': [metadata['full_filename']] if 'full_filename' in metadata else []
        }
        df.at[index, 'images_status']['trademark_status'][source_type]['count'] += 1
        # Ensure 'image_source' exists before accessing
        image_source = trademark_row.get('image_source', 'unknown')
        if image_source not in df.at[index, 'images_status']['trademark_status'][source_type]['sources']:
             df.at[index, 'images_status']['trademark_status'][source_type]['sources'][image_source] = 0 # Initialize if needed
        df.at[index, 'images_status']['trademark_status'][source_type]['sources'][image_source] += 1

        # --- Update ip_manager state ---
        location_id = f"{source_type}-{formatted_reg_no}"
        ip_manager.record_finding('trademark', location_id, [formatted_reg_no])

        return True

    return False