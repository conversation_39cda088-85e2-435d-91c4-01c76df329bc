# Alerts/LexisNexis/ScrapeDateRange.py
import pandas as pd
import datetime
import time
import logging

from selenium.common.exceptions import TimeoutException, NoSuchElementException
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait, Select
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.common.keys import Keys
# Selenium StaleElementReferenceException
from selenium.common.exceptions import StaleElementReferenceException


# Project-specific imports
from DatabaseManagement.ImportExport import get_table_from_GZ, insert_and_update_df_to_GZ_id
from Alerts.Chrome_Driver import send_keyboard_input, move_mouse_to, random_delay
from Alerts.LexisNexis.Search_Cases import wait_for_results_to_stabilize, go_to_next_page
from Alerts.LexisNexis.Login import get_logged_in_browser
from Alerts.LexisNexis.Get_Case_Details import extract_lexisnexis_permalink
from Common.Constants import court_mapping
from logdata import log_message


# Setup basic logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')


def scrape_date_range(start_date, end_date, driver_param=None, df_existing_cases=None, plaintiff_df=None):
    """
    Searches LexisNexis for cases based on "Schedule A" keyword, IP practice area,
    and date range. Filters cases based on title, existence in DB, and detailed
    case page content. Saves new cases to the database.

    Args:
        driver: Logged-in Selenium WebDriver instance.
        original_window (str): Handle of the original browser window (search results).
        start_date (datetime.date): Start date for the search.
        end_date (datetime.date): End date for the search.

    Returns:
        pd.DataFrame: DataFrame of the newly added cases, or empty DataFrame if none.
    """
    logging.info(f"Starting scrape_date_range for {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}")
    new_cases_data = []
    
    if driver_param is None:
        try: 
            driver = get_logged_in_browser()
        except Exception as e_login:
            logging.error(f"Failed to get logged-in browser: {e_login}")
            if driver in locals():
                driver.quit()
            return pd.DataFrame()
    else:
        driver = driver_param
    
    
    try: 
        if df_existing_cases is None:
            df_existing_cases = get_table_from_GZ("tb_case", force_refresh=True)
        if plaintiff_df is None:
            plaintiff_df = get_table_from_GZ("tb_plaintiff", force_refresh=True)
    except Exception as e_existing_cases:
        logging.error(f"Failed to load existing tables: {e_existing_cases}")
        return pd.DataFrame()

    try:
        original_window = driver.current_window_handle
        
        driver.get("https://advance.lexis.com/courtlinksearch")
        WebDriverWait(driver, 30).until(EC.presence_of_element_located((By.NAME, "keywords")))
        logging.info("Navigated to CourtLink search page.")

        keyword_input = driver.find_element(By.NAME, "keywords")
        keyword_input.clear()
        send_keyboard_input(driver, keyword_input, '"Schedule A"')
        logging.info("Entered keyword: 'Schedule A'")
        time.sleep(1)

        try:
            logging.info("Applying 'Intellectual Property' practice area filter...")
            filter_input_pa = WebDriverWait(driver, 10).until(
                EC.visibility_of_element_located((By.CSS_SELECTOR, "input.dropdowncontainersearch[placeholder='Type to filter Litigation Areas']"))
            )
            driver.execute_script("arguments[0].scrollIntoView(); window.scrollBy(0, -150);", filter_input_pa)
            filter_input_pa.click()
            time.sleep(0.5)
            filter_input_pa.send_keys("Intellectual Property")
            time.sleep(0.5)
            ip_option = WebDriverWait(driver, 10).until(
                EC.element_to_be_clickable((By.XPATH, "//span[contains(@class, 'markabletextlabel') and normalize-space()='Intellectual Property'] | //span[contains(@class, 'markabletextlabel')][.//mark[contains(text(),'Intellectual')]]"))
            )
            ip_option.click()
            logging.info("Selected 'Intellectual Property' practice area.")
            time.sleep(1) # Allow filter to apply and any UI changes to settle
            # Click body to close dropdown if it's open and potentially obscuring other elements
            driver.find_element(By.TAG_NAME, "body").click()
            time.sleep(0.5)
        except Exception as e_pa:
            logging.error(f"Could not apply Practice Area filter: {e_pa}. Proceeding without it, results may be broader.")

        try:
            date_filed_section = driver.find_element(By.XPATH, "//h2[text()='Date Filed']/ancestor::dateselector")
            select_element = date_filed_section.find_element(By.TAG_NAME, "select")
            select_obj = Select(select_element)
            select_obj.select_by_visible_text("Date is between")

            date_inputs = date_filed_section.find_elements(By.XPATH, ".//input[@type='text' and @aria-label='Enter the date']")
            start_input_box = date_inputs[0]
            driver.execute_script("arguments[0].scrollIntoView(); window.scrollBy(0, -150);", start_input_box)
            start_input_box.send_keys(Keys.CONTROL + 'a')
            start_input_box.send_keys(Keys.DELETE)
            send_keyboard_input(driver, start_input_box, start_date.strftime('%m%d%Y'))

            end_input_box = date_inputs[1]
            end_input_box.send_keys(Keys.CONTROL + 'a')
            end_input_box.send_keys(Keys.DELETE)
            send_keyboard_input(driver, end_input_box, end_date.strftime('%m%d%Y'))
            logging.info(f"Set date range: {start_date.strftime('%m/%d/%Y')} - {end_date.strftime('%m/%d/%Y')}")
            time.sleep(0.5)
            driver.find_element(By.TAG_NAME, "body").click() # Click body to close date picker
            time.sleep(0.5)
        except Exception as e_date:
            logging.error(f"Failed to set date range: {e_date}")
            return pd.DataFrame()

        search_button = WebDriverWait(driver, 20).until(
            EC.presence_of_element_located((By.ID, 'triggersearch'))
        )
        driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", search_button)
        time.sleep(0.5)
        
        # Use ActionChains to perform the click, which can be more reliable against overlays.
        actions = ActionChains(driver)
        actions.move_to_element(search_button).click().perform()
        logging.info("Search submitted. Waiting for results...")

        page_num = 1
        title_keywords_filter = ["schedule", "doe", "unknown", "unincorporated", "listed", "identified"]
        v_patterns_priority = [" v. ", " vs ", " v "]

        while True:
            logging.info(f"Processing results page {page_num}...")
            results = wait_for_results_to_stabilize(driver)
            if not results:
                logging.info("No results found on this page or stabilization failed.")
                if not go_to_next_page(driver):
                    logging.info("Confirmed no next page after empty results.")
                    break
                else:
                    page_num +=1
                    logging.info("Empty results, but moved to next page.")
                    continue

            logging.info(f"Found {len(results)} results on page {page_num}.")

            for idx, result_element in enumerate(results):
                title = "Unknown Title - Error Before Extraction" # Default title for logging if extraction fails early
                try:
                    driver.execute_script("arguments[0].scrollIntoViewIfNeeded(true); window.scrollBy(0, -150);", result_element)
                    time.sleep(0.2)

                    title_element = result_element.find_element(By.CSS_SELECTOR, "h2.doc-title a")
                    title = title_element.text.strip()

                    court, date_filed_obj, docket_number_cleaned = None, None, None
                    try:
                        metadata_element = result_element.find_element(By.CLASS_NAME, 'metadata')
                        spans = metadata_element.find_elements(By.TAG_NAME, 'span')
                        if len(spans) >= 3:
                            court_raw = spans[0].text.strip()
                            court = court_mapping.get(court_raw, court_raw)
                            date_filed_str = spans[1].text.strip()
                            date_filed_obj = pd.to_datetime(date_filed_str, errors='coerce').date()
                            
                            docket_str = spans[2].text.strip()
                            docket_number_cleaned = docket_str.replace("CV", "cv").replace("cv", "-cv-")
                            if "cv" not in docket_number_cleaned.lower():
                                logging.debug(f"Skipping case (no 'cv' in docket): {title} ({docket_str})")
                                continue
                            if "/" in docket_number_cleaned:
                                docket_number_cleaned = docket_number_cleaned.split("/")[1]
                            while len(docket_number_cleaned) < 13 and "-cv-" in docket_number_cleaned :
                                docket_number_cleaned = docket_number_cleaned.replace("-cv-", "-cv-0")
                        else:
                            logging.warning(f"Metadata spans incomplete for: {title}. Skipping.")
                            continue
                        if not all([court, date_filed_obj, docket_number_cleaned]):
                            logging.warning(f"Essential metadata missing for: {title}. Skipping.")
                            continue
                    except (NoSuchElementException, IndexError, ValueError) as e_meta:
                        logging.warning(f"Error extracting metadata for {title}: {e_meta}. Skipping.")
                        continue
                    
                    logging.debug(f"Extracted: {title}, {court}, {date_filed_obj}, {docket_number_cleaned}")

                    # Rule 1: Check if the case already exists in the database
                    is_existing = ((df_existing_cases['docket'] == str(docket_number_cleaned)) & \
                                   (df_existing_cases['court'] == str(court))).any()
                    if is_existing:
                        logging.debug(f"Skipping (already exists in DB): {title} ({docket_number_cleaned}, {court})")
                        continue

                    # Rule 2: Apply new filtering logic
                    passes_title_keywords = any(keyword in title.lower() for keyword in title_keywords_filter)
                    logging.debug(f"'{title}': Title keyword check = {passes_title_keywords}")

                    passes_et_al_in_suffix = False
                    passes_schedule_a_in_description_detail = False
                    ln_url_for_case = None # Initialize permalink for this case

                    if not passes_title_keywords:
                        # Only check 'et al' if title keywords didn't pass, for the second part of the OR condition
                        for v_pattern in v_patterns_priority:
                            if v_pattern in title.lower():
                                title_suffix = title.split(v_pattern, 1)[-1]
                                if "et al" in title_suffix.lower():
                                    passes_et_al_in_suffix = True
                                    break
                        logging.debug(f"'{title}': 'et al' in suffix check = {passes_et_al_in_suffix}")

                    # Determine if detail page needs to be visited:
                    # - If passes_title_keywords (for permalink, why? so that next time we don't have to do a single case search!)
                    # - OR if (NOT passes_title_keywords AND passes_et_al_in_suffix) (for Schedule A desc check AND permalink)
                    needs_detail_page_visit = passes_title_keywords or passes_et_al_in_suffix

                    if needs_detail_page_visit:
                        logging.debug(f"'{title}': Opening detail page. TitleKeywords: {passes_title_keywords}, EtAlInSuffix: {passes_et_al_in_suffix}")
                        
                        current_title_element_for_click = result_element.find_element(By.CSS_SELECTOR, "h2.doc-title a")
                        actions = ActionChains(driver)
                        actions.key_down(Keys.CONTROL).click(current_title_element_for_click).key_up(Keys.CONTROL).perform()
                        
                        try:
                            WebDriverWait(driver, 15).until(EC.number_of_windows_to_be(2))
                            driver.switch_to.window(driver.window_handles[-1])
                            WebDriverWait(driver, 30).until(EC.presence_of_element_located((By.ID, "Header")))
                            logging.debug(f"Detail page loaded for: {docket_number_cleaned}")
                            
                            if passes_title_keywords: # Case qualifies based on title keywords. Extract permalink.
                                ln_url_for_case = extract_lexisnexis_permalink(driver, docket_number_cleaned)
                            elif passes_et_al_in_suffix: # Implies not passes_title_keywords due to if/elif
                                # Case might qualify if 'Schedule A' is in description of steps
                                logging.debug(f"'{title}': Checking for 'Schedule A' in description on detail page.")
                                try:
                                    docket_table = driver.find_element(By.CLASS_NAME, "SS_DataTable")
                                    rows_detail = docket_table.find_elements(By.TAG_NAME, "tr") # Renamed to avoid conflict
                                    for row_idx_detail, row_detail in enumerate(rows_detail):
                                        if row_idx_detail == 0: continue # Skip header
                                        columns_detail = row_detail.find_elements(By.TAG_NAME, "td")
 
                                        # Break condition: row_idx_detail > 20 AND (step_date - date_filed_obj).days > 30
                                        if row_idx_detail > 20:
                                            # Ensure date_filed_obj is a valid date and step_date can be parsed
                                            if date_filed_obj and not pd.isna(date_filed_obj) and len(columns_detail) > 2:
                                                step_date_str = columns_detail[3].text.strip() 
                                                try:
                                                    step_date = pd.to_datetime(step_date_str, errors='raise').date()
                                                    if (step_date - date_filed_obj).days > 30:
                                                        logging.debug(f"Stopping detail scan (date rule): row {row_idx_detail} > 20 and step_date {step_date} is > 30 days after filed_date {date_filed_obj}.")
                                                        break # Break the inner loop (rows_detail)
                                                except ValueError: # Catches parsing errors for step_date
                                                    logging.warning(f"Could not parse step_date '{step_date_str}' for row {row_idx_detail}. Date-based break condition not applied for this row.")
                                            
                                        if len(columns_detail) > 4: # Description in 5th column (index 4)
                                            description_text_detail = columns_detail[4].text # Renamed
                                            if "Schedule A" in description_text_detail or 'Schedule "A"' in description_text_detail:
                                                passes_schedule_a_in_description_detail = True
                                                logging.debug(f"'Schedule A' in description PASSED for step {row_idx_detail} for: {docket_number_cleaned}")
                                                break
                                    
                                    if not passes_schedule_a_in_description_detail:
                                        logging.debug(f"'Schedule A' in description FAILED for: {docket_number_cleaned}")
                                    else:
                                        # 'Schedule A' found, and this path is active. Case will be kept. Extract permalink.
                                        ln_url_for_case = extract_lexisnexis_permalink(driver, docket_number_cleaned)

                                except NoSuchElementException:
                                    logging.warning(f"Docket steps table (SS_DataTable) not found for: {docket_number_cleaned} while checking for Schedule A.")
                                except Exception as e_step_detail:
                                    logging.warning(f"Error processing docket steps for {docket_number_cleaned} for Schedule A check: {e_step_detail}")
                        except TimeoutException:
                            logging.warning(f"Timed out loading case detail page for: {docket_number_cleaned}")
                        except Exception as e_detail_page:
                            logging.error(f"Error on case detail page for {docket_number_cleaned}: {e_detail_page}")
                        finally:
                            if len(driver.window_handles) > 1 and driver.current_window_handle != original_window:
                                driver.close()
                            driver.switch_to.window(original_window)
                    
                    # Final decision to keep the case
                    should_keep_this_case = False
                    reason_for_keeping = "N/A"

                    if passes_title_keywords:
                        should_keep_this_case = True
                        reason_for_keeping = "Title Keyword"
                    elif passes_et_al_in_suffix and passes_schedule_a_in_description_detail:
                        # This branch is only taken if passes_title_keywords was False
                        should_keep_this_case = True
                        reason_for_keeping = "'et al' in title and 'Schedule A' in description"

                    if should_keep_this_case:
                        log_message_qualify = f"QUALIFIED NEW CASE ({reason_for_keeping}): {title} ({docket_number_cleaned}, {court}, {date_filed_obj})"
                        if needs_detail_page_visit and ln_url_for_case is None:
                            logging.warning(f"{log_message_qualify} but permalink extraction FAILED.")
                        else:
                            logging.info(f"{log_message_qualify}, Link: {ln_url_for_case if ln_url_for_case else 'N/A'}")
                        
                        new_cases_data.append({
                            'title': title,
                            'date_filed': date_filed_obj,
                            'court': court,
                            'docket': docket_number_cleaned,
                            'ln_url': ln_url_for_case
                        })
                    else:
                        logging.info(f"Case did not qualify: {title}. Conditions: TitleKeywords={passes_title_keywords}, EtAlInSuffix={passes_et_al_in_suffix}, ScheduleAInDescription={passes_schedule_a_in_description_detail}")
                    
                    time.sleep(1) # Small delay before processing next result on page

                except StaleElementReferenceException:
                    logging.warning(f"StaleElementReferenceException for result item {idx} on page {page_num} for '{title}'. Re-fetching results for page might be needed or skipping item.")
                    # Consider breaking the inner loop and re-processing the page if this is common
                    continue 
                except Exception as e_result_item:
                    logging.error(f"Error processing result item {idx} on page {page_num} for '{title}': {e_result_item}", exc_info=True)
                    if len(driver.window_handles) > 1 and driver.current_window_handle != original_window:
                        logging.warning("Attempting to close potentially orphaned tab and switch back.")
                        try:
                            driver.close()
                            driver.switch_to.window(original_window)
                        except Exception as e_recover:
                            logging.error(f"Failed to recover by closing tab: {e_recover}")

            if not go_to_next_page(driver):
                logging.info("No more pages to process.")
                break
            page_num += 1
            time.sleep(2) # Delay between pages

        df_to_save = pd.DataFrame() # Initialize
        if new_cases_data:
            df_to_save = pd.DataFrame(new_cases_data)
            logging.info(f"Attempting to save {len(df_to_save)} new cases to tb_case.")
            try:
                df_saved_with_ids = insert_and_update_df_to_GZ_id(df_to_save, "tb_case", "docket", "court")
                logging.info(f"Successfully saved/updated {len(df_saved_with_ids)} cases. IDs: {df_saved_with_ids['id'].tolist() if 'id' in df_saved_with_ids and not df_saved_with_ids.empty else 'N/A'}")
                return df_saved_with_ids 
            except Exception as e_db:
                logging.error(f"Database save/update failed: {e_db}", exc_info=True)
                return df_to_save # Optionally return the df_to_save without IDs if save failed but data is useful
        else:
            logging.info("No new cases qualified to be saved.")
            return pd.DataFrame() # Return empty DataFrame

    except Exception as e_main:
        logging.error(f"An critical error occurred in scrape_date_range: {e_main}", exc_info=True)
        return pd.DataFrame() # Return empty DataFrame on critical error
    finally:
        if driver_param is None:
            logging.info("Closing browser.")
            driver.quit()

# Example usage (for testing, can be removed or commented out)
if __name__ == '__main__':
    import sys
    import os
    sys.path.append(os.getcwd())

    df_existing_cases = get_table_from_GZ("tb_case", force_refresh=True)
    df_plaintiffs = get_table_from_GZ("tb_plaintiff", force_refresh=True)

    overall_start_date = datetime.date(2023, 4, 2)
    overall_end_date = datetime.date(2025, 3, 20)
    
    current_loop_end_date = overall_end_date
    
    logging.info(f"Starting test scrape from {overall_start_date} to {overall_end_date} in 30-day chunks.")

    while current_loop_end_date >= overall_start_date:
        # Determine the start date for the current chunk
        current_loop_start_date = current_loop_end_date - datetime.timedelta(days=29)
        
        # Ensure the chunk's start date doesn't go before the overall start date
        if current_loop_start_date < overall_start_date:
            current_loop_start_date = overall_start_date
            
        logging.info(f"--- Processing chunk: {current_loop_start_date.strftime('%Y-%m-%d')} to {current_loop_end_date.strftime('%Y-%m-%d')} ---")
        
        # driver will be initialized and quit within scrape_date_range if not passed
        newly_added_df_chunk = scrape_date_range(current_loop_start_date, current_loop_end_date, 
            df_existing_cases=df_existing_cases, plaintiff_df=df_plaintiffs)
        
        if newly_added_df_chunk is not None and not newly_added_df_chunk.empty:
            logging.info(f"Added {len(newly_added_df_chunk)} cases from chunk {current_loop_start_date.strftime('%Y-%m-%d')} to {current_loop_end_date.strftime('%Y-%m-%d')}")
        else:
            logging.info(f"No new cases added from chunk {current_loop_start_date.strftime('%Y-%m-%d')} to {current_loop_end_date.strftime('%Y-%m-%d')}")
            
        # Move to the next chunk
        current_loop_end_date = current_loop_start_date - datetime.timedelta(days=1)
        
        time.sleep(5) # Small delay between chunks