from langfuse import observe
import langfuse 
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import pandas as pd
import time
import json
from datetime import date, datetime
import traceback
from Alerts.Chrome_Driver import move_mouse_to, random_delay
import re
from logdata import log_message # Ensure log_message is available
import Common.Constants as Constants
import pandas as pd # Ensure pandas is imported
from DatabaseManagement.ImportExport import get_subscibed_cases 
from time import sleep

@observe(capture_input=False, capture_output=False)
def get_case_details(cases_df, idx, driver, options):
    sleep(1)
    
    if 'refresh_days_threshold' in options:
        refresh_days_threshold = options['refresh_days_threshold']
    else:
        subscribed_cases_df = get_subscibed_cases()
        subscribed_cases_ids = list(set(subscribed_cases_df['id'].tolist()))
        if cases_df.at[idx, 'id'] in subscribed_cases_ids:
            refresh_days_threshold = 3
        else:
            refresh_days_threshold = 15
            
    refresh = False
    title_has_changed = False

    try:
        # Locate the table within the div with id "Header"
        header_div = WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, "Header")))
        refresh_date_str = driver.find_element(By.CSS_SELECTOR, 'p.SS_DocumentInfo span.SS_bf').text.split(' ')[-1]
        refresh_date = pd.to_datetime(refresh_date_str)

        # Check if we need to refresh the case
        if 'Class Code: Open' in header_div.text and (date.today() - refresh_date.date()).days >= int(refresh_days_threshold):
            log_message(f"Case {cases_df.at[idx, 'docket']} is open and needs to be refreshed")
            # Get current number of steps in the table
            try:
                steps_table = WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.CLASS_NAME, "SS_DataTable")))
                current_steps_count = len(steps_table.find_elements(By.TAG_NAME, 'tr'))

                try:
                    refresh_button = WebDriverWait(driver, 10).until(EC.element_to_be_clickable((By.CLASS_NAME, 'SS_DocketUpdateNow')))
                    move_mouse_to(driver, refresh_button)
                    refresh_button.click()
                    log_message(f"Refreshing {cases_df.at[idx, 'docket']} ...")
                
                    # I need to wait for the refresh_button to desapear
                    WebDriverWait(driver, 180).until(EC.invisibility_of_element_located((By.CLASS_NAME, 'SS_DocketUpdateNow')))
                    header_div = WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, "Header"))) # Re-fetch header after refresh
                    steps_table = WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.CLASS_NAME, "SS_DataTable")))
                    new_steps_count = len(steps_table.find_elements(By.TAG_NAME, 'tr'))
                    log_message(f"Refresh button desappeared => Case refreshed with {new_steps_count - current_steps_count} new steps")
                    random_delay()
                    log_message(f"After waiting 2 seconds => Case refreshed with {new_steps_count - current_steps_count} new steps")
                    
                    if new_steps_count > current_steps_count:
                        refresh = True

                    cases_df.at[idx, 'date_updated'] = datetime.now()
                
                except TimeoutException:
                    cases_df.at[idx, 'date_updated'] = refresh_date
                    log_message("Refresh button still visible after 180 seconds. Proceeding anyway.")
                    # header_div might be stale if refresh failed but page didn't error out
                    header_div = WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, "Header"))) # Attempt to re-fetch
        
                except Exception as e_refresh:
                    cases_df.at[idx, 'date_updated'] = refresh_date
                    log_message(f"Error during refresh attempt for {cases_df.at[idx, 'docket']}: {e_refresh}", level='WARNING')
                    # header_div might be stale
                    header_div = WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, "Header"))) # Attempt to re-fetch
                    
            except TimeoutException:
                log_message(f"This is a case without a steps table! {cases_df.at[idx, 'docket']}", level='WARNING')

        else:
            log_message(f"Case {cases_df.at[idx, 'docket']} is closed or not needs to be refreshed: Refreshed Date is {refresh_date} and threshold is {refresh_days_threshold}")
            cases_df.at[idx, 'date_updated'] = refresh_date

        cases_df.at[idx, 'date_checked'] = datetime.now()
        
        # Ensure title_element is found in the potentially updated header_div
        title_element = driver.find_element(By.CSS_SELECTOR, "header.SS_DocumentHeader #SS_DocumentTitle")
        new_title_val = title_element.text.split(',', 1)[-1].strip()

        if cases_df.at[idx, 'title'] != new_title_val:
            title_has_changed = True
        
        cases_df.at[idx, 'title'] = new_title_val
        
        table = header_div.find_element(By.TAG_NAME, "table")
        td_elements = table.find_elements(By.TAG_NAME, "td")

        # Process the first TD
        full_text_1 = td_elements[0].text
        lines_1 = full_text_1.split('\n')
        for line in lines_1:
            if 'Assigned To:' in line: cases_df.at[idx, 'assigned_to'] = line.replace('Assigned To:', '').strip()
            if 'Nature of Suit:' in line: cases_df.at[idx, 'nature_of_suit'] = line.replace('Nature of Suit:', '').strip()
            if 'Cause:' in line: cases_df.at[idx, 'cause'] = line.replace('Cause:', '').strip()

        # Process the second TD
        full_text_2 = td_elements[1].text
        lines_2 = full_text_2.split('\n')
        for line in lines_2:
            if 'Statute:' in line: cases_df.at[idx, 'statute'] = line.replace('Statute:', '').strip()
            if 'Demand Amount:' in line: cases_df.at[idx, 'demand_amount'] = line.replace('Demand Amount:', '').strip()
            if 'NOS Description:' in line: cases_df.at[idx, 'nos_description'] = line.replace('NOS Description:', '').strip()
            if 'Class Code:' in line: cases_df.at[idx, 'class_code'] = line.replace('Class Code:', '').strip()
            if 'Closed:' in line: cases_df.at[idx, 'closed'] = pd.to_datetime(line.replace('Closed:', '').strip()).date()

    except Exception as e:
        log_message(f"Error processing Header or Title in {cases_df.at[idx, 'docket']} from {cases_df.at[idx, 'court']}: {e}\n{traceback.format_exc()}",level='ERROR')
        # title_has_changed remains True (its initial value) because an error occurred.
        # Ensure date_checked is set even on error if not already
        if pd.notna(idx) and 'date_checked' in cases_df.columns:
             if pd.isna(cases_df.at[idx, 'date_checked']): # Check if it's NaN before overwriting
                  cases_df.at[idx, 'date_checked'] = datetime.now()
        # refresh status would be its last set value (likely False if error is early)

        
    # Extract plaintiff and defendant lawyers and names
    try:
        plaintiffNames = []
        plaintiffLawyers = []
        defendentNames = []
        defendentLawyers = []
        
        # Locate the litigants table within the div with id="Participants"
        participants_div = driver.find_element(By.ID, "Participants")
        litigants_table = participants_div.find_element(By.XPATH, ".//table[@data-housestyle='Table']")

        for table_row in litigants_table.find_elements(By.TAG_NAME, 'tr'):
            cells = table_row.find_elements(By.TAG_NAME, 'td')
            if len(cells) == 2:
                role = cells[0].text
                if "Plaintiff" in role:
                    plaintiffNames.append(cells[0].text.strip().replace("\nPlaintiff", ""))
                    plaintiffLawyers.append(get_plaintiff_law_firms(cells[1].text.strip()))
                elif "Defendant" in role:
                    defendentNames.append(cells[0].text.strip().replace("\nDefendant", ""))
                    defendentLawyers.append(cells[1].text.strip())

        cases_df.at[idx, 'plaintiff_names'] = json.dumps([name for name in plaintiffNames])
        cases_df.at[idx, 'plaintiff_lawyers'] = json.dumps([lawyer for lawyer in plaintiffLawyers])
        cases_df.at[idx, 'defendant_names'] = json.dumps([name for name in defendentNames])
        cases_df.at[idx, 'defendant_lawyers'] = json.dumps([lawyer for lawyer in defendentLawyers])

        # Get AI summary if there is one
        try:
            log_message(f"Looking for AI Summary element for {cases_df.at[idx, 'docket']}", level='DEBUG')
            # Wait up to 10 seconds for the AIDocSummary element to be present
            summary_div = WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, "AIDocSummary")))
            log_message(f"AI Summary element found for {cases_df.at[idx, 'docket']}", level='DEBUG')
            driver.save_screenshot(f"aisummary_found_{idx}.png")

            if pd.isna(cases_df.at[idx, 'aisummary']) or cases_df.at[idx, 'aisummary'] == "":
                # Wait for the div elements to be present within the summary_div
                div_elements = WebDriverWait(driver, 5).until(lambda d: summary_div.find_elements(By.TAG_NAME, "div"))
                if len(div_elements) > 1:
                    ai_summary_text = div_elements[1].text.strip()
                    if ai_summary_text:  # Only update if we actually got text
                        cases_df.at[idx, 'aisummary'] = ai_summary_text
                        log_message(f"AI Summary extracted for {cases_df.at[idx, 'docket']}: {ai_summary_text}", level='DEBUG')
                    else:
                        log_message(f"AI Summary div found but empty for {cases_df.at[idx, 'docket']}", level='WARNING')
                else:
                    log_message(f"AI Summary div found but insufficient child divs for {cases_df.at[idx, 'docket']}", level='WARNING')
            else:
                log_message(f"AI Summary already exists for {cases_df.at[idx, 'docket']}, skipping extraction", level='DEBUG')
            
            driver.execute_script("arguments[0].scrollIntoView(true);", summary_div)  # Scroll into view

        except Exception as e:
            log_message(f"Unexpected error retrieving AI Summary for {cases_df.at[idx, 'docket']}: {type(e).__name__}: {e}", level='ERROR')
            driver.save_screenshot(f"aisummary_error_{idx}.png")
            if not cases_df.at[idx, 'aisummary'] or cases_df.at[idx, 'aisummary'] == "": # Do not overwrite the AI summary
                cases_df.at[idx, 'aisummary'] = None
            driver.execute_script("arguments[0].scrollIntoView(true);", participants_div)  # Scroll into view

    except Exception as e:
        log_message(f"Error processing Participants in {cases_df.at[idx, 'docket']} from {cases_df.at[idx, 'court']}: {e}\n{traceback.format_exc()}",level='ERROR')

    # Check if ln_url is missing for the current case (NaN, None, or empty string)
    # Ensure index is valid before trying to access df.loc[index, 'ln_url']
    current_ln_url = cases_df.at[idx,  'ln_url']
    
    if pd.isna(current_ln_url) or not current_ln_url:
        log_message(f"ln_url is missing for {cases_df.at[idx, 'docket']}. Attempting to extract permalink.", level='INFO')
        extracted_url = extract_lexisnexis_permalink(driver, cases_df.at[idx, 'docket'])
        if extracted_url:
            cases_df.at[idx,  'ln_url'] = extracted_url
            log_message(f"Successfully extracted and stored ln_url for {cases_df.at[idx, 'docket']}: {extracted_url}", level='INFO')
        else:
            log_message(f"Failed to extract ln_url for {cases_df.at[idx, 'docket']} within get_case_details.", level='WARNING')
    else:
        log_message(f"ln_url already exists for {cases_df.at[idx, 'docket']}: {current_ln_url}", level='DEBUG')

    langfuse.get_client().update_current_span(output={"refresh_status": refresh, "title_has_changed": title_has_changed, "ai_summary": cases_df.at[idx, 'aisummary']})
    return refresh, title_has_changed

def get_plaintiff_law_firms(plaintiffLawyer):
    match = re.search(r"TO BE NOTICED(?:\s+|<br>)(.+?)(?:\s+|<br>.+?)\d+", plaintiffLawyer)
    if match:
        return match.group(1).strip()  # Return the captured text after "TO BE NOTICED"
    else:
        return plaintiffLawyer
    
def fix_plaintiff_law_firms():
    from DatabaseManagement.ImportExport import get_table_from_GZ, insert_and_update_df_to_GZ_batch
    case_df = get_table_from_GZ("tb_case")
    for index, row in case_df.iterrows():
        if "TO BE NOTICED" in row['plaintiff_lawyers']:
            list_of_law_firms = json.loads(row['plaintiff_lawyers'])
            for law_firm in list_of_law_firms:
                match = re.search(r"TO BE NOTICED(?:\s+|\n)(.+?)(?:\s+|\n).+?\d+", law_firm)
                if match:
                    list_of_law_firms[list_of_law_firms.index(law_firm)] = match.group(1).strip()
            
            case_df.loc[index, 'plaintiff_lawyers'] = json.dumps(list_of_law_firms)

        elif "<br>" in row['plaintiff_lawyers'] or "\n" in row['plaintiff_lawyers']:
            print("what to do?")

    insert_and_update_df_to_GZ_batch(case_df, "tb_case", "id")

def extract_lexisnexis_permalink(driver, identifier_for_logging: str, max_retries: int = 2):
    """
    Extracts the permalink for the current page on LexisNexis with retries.

    Args:
        driver: The Selenium WebDriver instance.
        identifier_for_logging (str): A string identifier (e.g., docket number) for logging purposes.
        max_retries (int): Maximum number of times to retry the extraction.

    Returns:
        str: The permalink URL if successful, None otherwise.
    """
    ln_url = None
    for attempt in range(max_retries):
        try:
            # Click "Actions" button
            actions_button = WebDriverWait(driver, 10).until(EC.element_to_be_clickable((By.ID, "documentactiondropdown")))
            actions_button.click()
            # log_message(f"Attempt {attempt + 1}: Clicked 'Actions' button for {identifier_for_logging}", level='DEBUG')

            # Click "Link to this page" (permalink) button
            permalink_button = WebDriverWait(driver, 10).until(EC.element_to_be_clickable((By.CSS_SELECTOR, "button[data-id='permalink']")))
            permalink_button.click()
            # log_message(f"Attempt {attempt + 1}: Clicked 'Link to this page' button for {identifier_for_logging}", level='DEBUG')

            # Wait for the permalink dialog to be visible
            permalink_dialog_locator = (By.CSS_SELECTOR, "aside.permalink-dialog")
            WebDriverWait(driver, 10).until(EC.visibility_of_element_located(permalink_dialog_locator))
            # log_message(f"Attempt {attempt + 1}: Permalink dialog is visible for {identifier_for_logging}", level='DEBUG')

            # Get permalink URL from the input field
            permalink_dialog_input_locator = (By.CSS_SELECTOR, "aside.permalink-dialog input[type='text']")
            permalink_dialog_input = WebDriverWait(driver, 10).until(EC.visibility_of_element_located(permalink_dialog_input_locator))

            # Wait until the input field has a non-empty value
            WebDriverWait(driver, 20).until(lambda d: d.find_element(*permalink_dialog_input_locator).get_attribute('value') != '')
            
            # I do not know why, but it is CRITICAL to reaquire the permalink_dialog_input after the wait. Otherwise you get stale element everytime
            permalink_dialog_input = WebDriverWait(driver, 10).until(EC.visibility_of_element_located(permalink_dialog_input_locator))
            ln_url = permalink_dialog_input.get_attribute('value')
            print(f"got value {ln_url}")
            # log_message(f"Attempt {attempt + 1}: Extracted permalink for {identifier_for_logging}: {ln_url}", level='INFO')

            # Close permalink dialog using the footer 'Close' button
            close_permalink_dialog_button_locator = (By.CSS_SELECTOR, "aside.permalink-dialog button[data-action='confirm']")
            close_permalink_dialog_button = WebDriverWait(driver, 10).until(EC.element_to_be_clickable(close_permalink_dialog_button_locator))
            close_permalink_dialog_button.click()
            # log_message(f"Attempt {attempt + 1}: Clicked 'Close' on permalink dialog for {identifier_for_logging}", level='DEBUG')

            # Wait for the permalink dialog to become invisible
            WebDriverWait(driver, 10).until(EC.invisibility_of_element_located(permalink_dialog_locator))
            # log_message(f"Attempt {attempt + 1}: Permalink dialog is invisible for {identifier_for_logging}", level='DEBUG')
            return ln_url # Success
        
        except TimeoutException as e_permalink_timeout:
            log_message(f"❌ Attempt {attempt + 1} failed for {identifier_for_logging}: Timeout - {e_permalink_timeout}", level='WARNING')
        except NoSuchElementException as e_permalink_no_such:
            log_message(f"❌ Attempt {attempt + 1} failed for {identifier_for_logging}: NoSuchElement - {e_permalink_no_such}", level='WARNING')
        except Exception as e_permalink:
            log_message(f"❌ Attempt {attempt + 1} failed for {identifier_for_logging}: Error - {e_permalink}", level='ERROR')
        
        # Common error handling: try to close dialog if open, then continue to next retry if applicable
        if attempt < max_retries - 1:
            log_message(f"⚠️ Retrying permalink extraction for {identifier_for_logging} (attempt {attempt + 2}/{max_retries})", level='INFO')
            try:
                # Attempt to close the dialog using the footer button if it's still present
                # Check if dialog is visible before trying to close
                dialog_element = driver.find_elements(*permalink_dialog_locator) # Use find_elements to avoid exception if not found
                if dialog_element and dialog_element[0].is_displayed():
                    confirm_button_footer = driver.find_element(By.CSS_SELECTOR, "aside.permalink-dialog button[data-action='confirm']")
                    if confirm_button_footer.is_displayed() and confirm_button_footer.is_enabled():
                        confirm_button_footer.click()
                        log_message(f"⚠️ Attempted to close permalink dialog via footer confirm button after error during retry attempt {attempt + 1} for {identifier_for_logging}", level='DEBUG')
                        WebDriverWait(driver, 5).until(EC.invisibility_of_element_located(permalink_dialog_locator)) # Short wait for close
            except Exception as e_close_dialog:
                log_message(f"❌ Could not close permalink dialog after error during retry attempt {attempt + 1} for {identifier_for_logging}: {e_close_dialog}", level='DEBUG')
            time.sleep(1) # Small delay before retrying
        else:
            log_message(f"❌❌❌ All {max_retries} attempts to extract permalink for {identifier_for_logging} failed.", level='ERROR')
            # Final attempt to close dialog if it was the last retry and failed
            try:
                dialog_element = driver.find_elements(*permalink_dialog_locator)
                if dialog_element and dialog_element[0].is_displayed():
                    confirm_button_footer = driver.find_element(By.CSS_SELECTOR, "aside.permalink-dialog button[data-action='confirm']")
                    if confirm_button_footer.is_displayed() and confirm_button_footer.is_enabled():
                        confirm_button_footer.click()
                        log_message(f"Final attempt to close permalink dialog via footer confirm button for {identifier_for_logging}", level='DEBUG')
            except Exception:
                log_message(f"❌ Could not close permalink dialog on final failed attempt for {identifier_for_logging}", level='DEBUG')

    return ln_url # Returns None if all retries fail
    
if __name__ == "__main__":
    # text = "Sofia Quezada Hastings<br>ATTORNEY TO BE NOTICED<br>Aronberg Goldgehn Davis &amp; Garmisa<br>Aronberg Goldgehn Davis &amp; Garmisa 225 W. Washington St. Suite 2800 60606<br>Chicago, IL  60606<br>USA<br>312-755-3139 Email:<EMAIL>"
    # text2= "John Wilson<br>ATTORNEY TO BE NOTICED<br>Hughes Socol Piers Resnick &amp; Dym, Ltd.<br>70 W Madison St 4000<br>Chicago, IL  60602<br>USA<br>312-604-2668 Fax: Not A Member Email:<EMAIL><br><br>Robert Payton Mcmurray<br>ATTORNEY TO BE NOTICED<br>Hughes Socol Piers Resnick &amp; Dym, Ltd.<br>70 W Madison St. Ste 4000<br>Chicago, IL  60602<br>USA<br>(312) 604-2696 Fax: Not A Member Email:<EMAIL><br><br>William Benjamin Kalbac<br>ATTORNEY TO BE NOTICED<br>Hughes Socol Piers Resnick &amp; Dym, Ltd.<br>70 W. Madison St. Suite#4000<br>Chicago, IL  60602<br>USA<br>(312)580-0100 Fax: Not A Member Email:<EMAIL><br><br>Michael A. Hierl<br>ATTORNEY TO BE NOTICED<br>Hughes Socol PIers Resnick &amp; Dym Ltd<br>Three First National Plaza 70 West Madison Street Suite 4000<br>Chicago, IL  60602<br>USA<br>312 580 0100 Fax: Active Email:<EMAIL>"
    # print(get_plaintiff_law_firms(text))
    fix_plaintiff_law_firms()
