import random
import time
import os
import undetected_chromedriver as uc
from selenium.webdriver.common.action_chains import ActionChains
import datetime


def get_driver():
    if os.name == 'nt':
        driver = uc.Chrome(options=set_chrome_options())
    else:
        driver = uc.Chrome(driver_executable_path="/root/.local/share/undetected_chromedriver/chromedriver_copy", options=set_chrome_options())

    return driver

def driver_is_alive(driver):
    try:
        driver.current_url
        return True
    except Exception:
        return False

def set_chrome_options():
    # Set up options for undetected ChromeDriver
    chrome_options = uc.ChromeOptions()
    chrome_options.add_argument("--start-maximized")  # Start in full-screen mode
    if os.name != 'nt':
        chrome_options.add_argument('--headless=new')  # Run in headless mode
    chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/129.0.0.0 Safari/537.36")  # Fixed user agent
    chrome_options.add_argument("--window-size=3840,2160")  # Simulate 4K resolution
    chrome_options.add_argument("--no-sandbox")  # The sandbox is a security feature in Chrome that adds an extra layer of protection by isolating the browser from the underlying system. In a Docker container, the sandbox can cause issues because it requires certain kernel capabilities that might not be available.
    chrome_options.add_argument("--disable-dev-shm-usage") # /dev/shm is a shared memory space used by Chrome for rendering. In Docker containers, this space is often limited (usually 64MB), which can cause Chrome to crash on memory-intensive pages. This flag tells Chrome to disable the use of /dev/shm and instead use /tmp for shared memory, which is less likely to have size restrictions.
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--disable-notifications") # Disable browser notifications
    chrome_options.add_argument("--disable-popup-blocking") # Disable popup blocking
    chrome_options.add_argument("--disable-extensions")

    prefs = {
        "download.prompt_for_download": False,
        "download.directory_upgrade": True,
        "safebrowsing.enabled": True,
        "plugins.always_open_pdf_externally": True,
        "download.conflict_resolution": 2  # 2 means overwrite, 0 means prompt, 1 means uniquify
    }
    chrome_options.add_experimental_option("prefs", prefs)
    return chrome_options



def set_download_directory(driver, download_dir):
    driver.execute_cdp_cmd("Page.setDownloadBehavior", {
        "behavior": "allow",
        "downloadPath": download_dir
    })


# Randomize mouse movement before clicking an element
# def move_mouse_to(actions, element):
#     # Move to the center of the element first
#     actions.move_to_element(element).perform()
    
#     for _ in range(random.randint(2, 5)):  # Random small movements
#         x_offset = random.randint(-10, 10)
#         y_offset = random.randint(-10, 10)
#         actions.move_by_offset(x_offset, y_offset)
#         actions.perform()
    
#     # Ensure the final position is on the element
#     actions.move_to_element(element).perform()

# Randomize mouse movement before clicking an element
def move_mouse_to(driver, element):
    driver.execute_script("arguments[0].scrollIntoView(); window.scrollBy(0, -300);", element)
    actions = ActionChains(driver)
    # Move to the center of the element first
    actions.move_to_element(element)
    
    for _ in range(random.randint(2, 5)):  # Random small movements
        x_offset = random.randint(-10, 10)
        y_offset = random.randint(-10, 10)
        actions.move_by_offset(x_offset, y_offset)
    
    # Ensure the final position is on the element
    actions.move_to_element(element)
    actions.perform()

def send_keyboard_input(driver, element, text):
    move_mouse_to(driver, element)
    element.click()
    for char in text:
        element.send_keys(char)
        time.sleep(random.uniform(0.1, 0.3))  # Adjust the delay as needed

# Simulate random delay between actions
def random_delay(min_delay=1, max_delay=2):
    time.sleep(random.uniform(min_delay, max_delay))
    
    
def save_browser_state(driver, error_msg, my_traceback):
    # Create error directory with timestamp
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    error_dir = os.path.join(os.getcwd(), 'Errors', timestamp)
    os.makedirs(error_dir, exist_ok=True)
    
    # Save info about each tab
    tab_info = []
    for i, handle in enumerate(driver.window_handles):
        driver.switch_to.window(handle)
        tab_info.append({
            'tab_number': i,
            'url': driver.current_url,
            'title': driver.title
        })
        
        # Save HTML content
        html_file = os.path.join(error_dir,f"tab_{i}.html")
        with open(html_file, 'w', encoding='utf-8') as f:
            f.write(driver.page_source)
    
    # Save tab summary
    summary = os.path.join(error_dir, "tab_summary.txt")
    with open(summary, 'w', encoding='utf-8') as f:
        f.write(f"Error: {error_msg}\n\n")
        for tab in tab_info:
            f.write(f"Tab {tab['tab_number']}:\n")
            f.write(f"  Title: {tab['title']}\n")
            f.write(f"  URL: {tab['url']}\n\n")
        f.write("Traceback:\n")
        f.write(my_traceback)
    
    return error_dir

if __name__ == '__main__':
    driver = get_driver()