import os
import sys
import shutil
import asyncio
from tqdm import tqdm

# Add the root directory to the Python path
sys.path.append(os.getcwd())

from DatabaseManagement.ImportExport import get_table_from_GZ
from Check.RAG.Collect_Images import get_docket_from_filename, get_case_image_from_df, sanitize_name
from Common.Constants import local_ip_tro_folder
from Check.Do_Check_Download import download_from_url
from Common.Constants import sem_task

async def main():
    """
    Main function to check files in the production folder, move bad files,
    and download correct files.
    """
    CASE_TYPE = "copyrights"
    PRODUCTION_FOLDER = os.path.join(local_ip_tro_folder, CASE_TYPE, "Production")
    BAD_FILES_FOLDER = os.path.join(local_ip_tro_folder, CASE_TYPE, "bad_files")
    GOOD_FILES_FOLDER = os.path.join(local_ip_tro_folder, CASE_TYPE, "good_files")

    os.makedirs(BAD_FILES_FOLDER, exist_ok=True)
    os.makedirs(GOOD_FILES_FOLDER, exist_ok=True)

    print("Loading case data...")
    cases_df = get_table_from_GZ("tb_case", force_refresh=False)
    print("Case data loaded.")

    production_files = os.listdir(PRODUCTION_FOLDER)
    
    # Group files by docket to process them in batches
    files_by_docket = {}
    for filename in production_files:
        docket = get_docket_from_filename(filename)
        if docket:
            if docket not in files_by_docket:
                files_by_docket[docket] = []
            files_by_docket[docket].append(filename)

    processed_dockets = set()
    download_tasks = []
    semaphore = asyncio.Semaphore(20)

    print(f"Checking {len(production_files)} files in {PRODUCTION_FOLDER}...")
    for docket, filenames in tqdm(files_by_docket.items(), desc="Processing dockets"):
        if docket in processed_dockets:
            continue

        # We only need to check one file per docket
        filename_to_check = filenames[0]
        
        case_row, case_image, original_image_key = get_case_image_from_df(cases_df, docket, filename_to_check, CASE_TYPE)

        if case_row is None or case_image is None:
            print(f"\nNo DB entry for docket {docket}. Moving files and redownloading.")
            
            # 1. Move all files for this docket to bad_files
            for f in filenames:
                src_path = os.path.join(PRODUCTION_FOLDER, f)
                dst_path = os.path.join(BAD_FILES_FOLDER, f)
                if os.path.exists(src_path):
                    shutil.move(src_path, dst_path)
            
            # 2. Find the case in the dataframe and download the correct files
            case_rows_for_docket = cases_df[cases_df['docket'] == docket]
            if not case_rows_for_docket.empty:
                correct_case_row = case_rows_for_docket.iloc[0]
                if 'images' in correct_case_row and isinstance(correct_case_row['images'], dict) and CASE_TYPE in correct_case_row['images']:
                    images_to_download = correct_case_row['images'][CASE_TYPE]
                    plaintiff_id = int(correct_case_row['plaintiff_id'])
                    docket_sanitized = docket.replace(':', '_')

                    for image_key, image_data in images_to_download.items():
                        new_filename_stem = f"{plaintiff_id}_{docket_sanitized}_{image_key}"
                        
                        new_filename = sanitize_name(new_filename_stem)
                        target_path = os.path.join(GOOD_FILES_FOLDER, new_filename)
                        
                        if not os.path.exists(target_path):
                            url = f"http://troimages-1329604052.cos.ap-guangzhou.myqcloud.com/plaintiff_images/{plaintiff_id}/high/{image_key}"
                            download_tasks.append(sem_task(semaphore, download_from_url(url, target_path)))

            processed_dockets.add(docket)

    if download_tasks:
        print(f"\nDownloading {len(download_tasks)} new files...")
        await asyncio.gather(*download_tasks)
        print("Downloads complete.")
    else:
        print("\nNo new files to download.")

    print("Script finished.")

if __name__ == "__main__":
    asyncio.run(main())