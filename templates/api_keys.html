{% extends 'layout_ui.html' %}

{% block title %}API Key Management{% endblock %}

{% block head %}
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/common.css') }}">
    <style>
        .api-keys-container {
            display: flex;
            gap: 2rem;
        }
        .keys-list {
            flex-grow: 1;
        }
        .add-key-form {
            flex-shrink: 0;
            width: 300px;
        }
        .form-group {
            margin-bottom: 1rem;
        }
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
        }
        .form-group input {
            width: 100%;
            padding: 0.5rem;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
    </style>
{% endblock %}

{% block content %}
<div class="app-container">
    <h1>API Key Management</h1>

    <div class="api-keys-container">
        <div class="keys-list">
            <h2>Existing API Keys</h2>
            <table class="common-table" id="api-keys-table">
                <thead>
                    <tr>
                        <th>API Key</th>
                        <th>Client Name</th>
                        <th>Client ID</th>
                        <th>Created At</th>
                        <th>Total Usage</th>
                        <th>Current Month</th>
                        <th>Previous Month</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- Rows will be populated by JavaScript -->
                </tbody>
            </table>
        </div>

        <div class="add-key-form">
            <h2>Add New API Key</h2>
            <form id="add-key-form">
                <div class="form-group">
                    <label for="client_name">Client Name:</label>
                    <input type="text" id="client_name" name="client_name" required>
                </div>
                <div class="form-group">
                    <label for="client_id">Client ID:</label>
                    <input type="number" id="client_id" name="client_id" required>
                </div>
                <div class="form-group">
                    <label for="rate_limit">Rate Limit (per second):</label>
                    <input type="number" id="rate_limit" name="rate_limit" value="1">
                </div>
                <div class="form-group">
                    <label for="daily_limit">Daily Limit:</label>
                    <input type="number" id="daily_limit" name="daily_limit" value="100">
                </div>
                <button type="submit" class="btn primary">Add Key</button>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
    <script src="{{ url_for('static', filename='js/api_keys.js') }}"></script>
{% endblock %}