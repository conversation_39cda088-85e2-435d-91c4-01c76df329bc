import uuid
from flask import Flask, request, jsonify, render_template, Blueprint
from auth_decorators import login_required
from Qdrant.api.utils.db import get_db_connection
from datetime import datetime, timedelta
import pandas as pd

# Create a Blueprint for API keys routes
api_keys_bp = Blueprint('api_keys', __name__, template_folder='templates')

def init_api_keys_routes(app: Flask):
    """Registers the API keys blueprint with the Flask app."""
    app.register_blueprint(api_keys_bp)
    return app

@api_keys_bp.route('/api_keys')
@login_required
def api_keys_page():
    """Renders the API keys management page."""
    return render_template('api_keys.html')

@api_keys_bp.route('/api/api_keys', methods=['GET'])
@login_required
def get_api_keys():
    """API endpoint to get all API keys and their usage stats."""
    conn = None
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # Get current and previous month start dates
        today = datetime.today()
        current_month_start = today.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        previous_month_end = current_month_start - timedelta(days=1)
        previous_month_start = previous_month_end.replace(day=1)

        # Fetch all API keys
        cursor.execute("SELECT api_key, client_id, client_name, rate_limit, daily_limit, created_at FROM public.check_client_api_keys ORDER BY created_at DESC")
        keys = cursor.fetchall()
        
        results = []
        for key in keys:
            api_key, client_id, client_name, rate_limit, daily_limit, created_at = key

            # Get total historical usage
            cursor.execute("SELECT COUNT(*) FROM public.check_api_requests WHERE api_key = %s", (api_key,))
            total_usage = cursor.fetchone()[0]

            # Get current month usage
            cursor.execute(
                "SELECT COUNT(*) FROM public.check_api_requests WHERE api_key = %s AND create_time >= %s",
                (api_key, current_month_start)
            )
            current_month_usage = cursor.fetchone()[0]

            # Get previous month usage
            cursor.execute(
                "SELECT COUNT(*) FROM public.check_api_requests WHERE api_key = %s AND create_time >= %s AND create_time < %s",
                (api_key, previous_month_start, current_month_start)
            )
            previous_month_usage = cursor.fetchone()[0]

            results.append({
                'api_key': api_key,
                'client_id': client_id,
                'client_name': client_name,
                'rate_limit': rate_limit,
                'daily_limit': daily_limit,
                'created_at': created_at.isoformat(),
                'total_usage': total_usage,
                'current_month_usage': current_month_usage,
                'previous_month_usage': previous_month_usage
            })

        cursor.close()
        return jsonify(results)

    except Exception as e:
        return jsonify({'error': str(e)}), 500
    finally:
        if conn:
            conn.close()


@api_keys_bp.route('/api/api_keys', methods=['POST'])
@login_required
def add_api_key():
    """API endpoint to create a new API key."""
    data = request.get_json()
    client_id = data.get('client_id')
    client_name = data.get('client_name')
    rate_limit = data.get('rate_limit', 1)
    daily_limit = data.get('daily_limit', 100)

    if not client_id or not client_name:
        return jsonify({'error': 'Client ID and Client Name are required'}), 400

    new_api_key = str(uuid.uuid4())
    conn = None
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute(
            """
            INSERT INTO public.check_client_api_keys (api_key, client_id, client_name, rate_limit, daily_limit)
            VALUES (%s, %s, %s, %s, %s)
            """,
            (new_api_key, client_id, client_name, rate_limit, daily_limit)
        )
        conn.commit()
        cursor.close()
        return jsonify({'status': 'success', 'api_key': new_api_key})
    except Exception as e:
        if conn:
            conn.rollback()
        return jsonify({'error': str(e)}), 500
    finally:
        if conn:
            conn.close()


@api_keys_bp.route('/api/api_keys/<string:api_key>', methods=['DELETE'])
@login_required
def delete_api_key(api_key):
    """API endpoint to delete an API key if it has no usage."""
    conn = None
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # Check for historical usage
        cursor.execute("SELECT COUNT(*) FROM public.check_api_requests WHERE api_key = %s", (api_key,))
        total_usage = cursor.fetchone()[0]

        if total_usage > 0:
            return jsonify({'error': 'Cannot delete API key with historical usage.'}), 400

        # Delete the key
        cursor.execute("DELETE FROM public.check_client_api_keys WHERE api_key = %s", (api_key,))
        conn.commit()
        
        if cursor.rowcount == 0:
            return jsonify({'error': 'API key not found.'}), 404

        cursor.close()
        return jsonify({'status': 'success'})
    except Exception as e:
        if conn:
            conn.rollback()
        return jsonify({'error': str(e)}), 500
    finally:
        if conn:
            conn.close()