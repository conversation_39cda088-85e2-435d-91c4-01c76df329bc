from langfuse import observe
import langfuse
import ast

from AI.GCV_GetImageParts import get_image_parts_async
from AI.GC_VertexAI import vertex_genai_multi_async
from AI.LLM_shared import get_json
import shutil, time, os, asyncio
import Common.Constants as Constants
from Check.RAG.qdrant_search import find_similar_assets_qdrant
from Check.Create_Report import create_check_report, create_product_url
from Check.Marks import get_all_matches, get_matches_and_check_perfect
from Check.Data_Cache import get_cached_int_cls_definitions_df
from FileManagement.Tencent_COS import async_upload_file_with_retry
from Check.Utils import create_ip_url

def format_trademark_matches_for_ai(matches):
    """Format trademark matches with metadata for AI analysis."""
    if not matches:
        return []

    formatted_matches = []

    for match in matches:
        _, _, metadata = match  # We don't need start_idx and end_idx for this purpose
        # metadata format: (normalized_mark_text, plaintiff_id, reg_no, ser_no, applicant_name, int_cls, goods_services_text_daily)
        text, plaintiff_id, reg_no, ser_no, applicant_name, int_cls, goods_services_text_daily = metadata

        formatted_match = {
            "text": text,
            "applicant_name": applicant_name or "Not available",
            "int_cls_list": int_cls,
            "goods_services": goods_services_text_daily or "Not available",
            "plaintiff_id": plaintiff_id,
            "reg_no": reg_no,
            "ser_no": ser_no
        }
        formatted_matches.append(formatted_match)

    return formatted_matches

@observe(capture_input=False, capture_output=False)
async def analyze_trademark_text_relevance(check_id, product_image_path, description, keywords, reference_text, all_matches):
    """
    Analyze trademark matches for infringement relevance using AI.
    Returns list of relevant trademark matches with 'report' field added.
    """
    langfuse.get_client().update_current_span(input={
        "check_id": check_id, "product_image_path": product_image_path,
        "description": description, "keywords": keywords, "reference_text": reference_text,
        "all_matches_count": len(all_matches)
    })
    if not all_matches:
        return []

    # Format matches for AI analysis
    formatted_matches = format_trademark_matches_for_ai(all_matches)

    if not formatted_matches:
        return []

    # Create the AI prompt for trademark infringement analysis
    prompt = f"""You are a Trademark attorney specialized in analyzing potential trademark infringement cases.

        **Task:** Analyze the registered trademarks against a product being sold online to identify which trademarks might be relevant from an infringement perspective.

        **Analysis Criteria:**
        For each trademark, consider the following factors to determine infringement relevance:

        1. **Similarity of the Marks:** Compare the trademark text with the product description, keywords, and reference text in terms of:
        - Spelling similarity
        - Sound (phonetic similarity)
        - Meaning (connotation, overall commercial impression)

        2. **Proximity/Relatedness of Goods:** Analyze the relationship between the goods offered under the registered trademark (based on the Goods and Services description) and the product being sold:
        - Are they directly competitive, related, complementary, or unrelated?

        3. **Strength of the Trademark:** Consider the distinctiveness of the trademark:
        - Is it fanciful, arbitrary, suggestive, descriptive, or generic?

        4. **Likelihood of Consumer Confusion:** Would consumers be likely to confuse the product with goods/services offered under the registered trademark?

        **Instructions:**
        - Only select trademarks that have a reasonable likelihood of infringement based on the above criteria
        - Focus on trademarks that are actually present or similar to text in the product description, keywords, or reference text
        - Consider the relatedness of goods/services based on the provided "Goods and Services" description.

        **Response Format:**
        Return a JSON array with the Tradenamrk_nb that you consider relevant for potential infringement analysis in the following format:

        {{"relevant_trademark_nbs": [1, 3, 5]}}

        If no trademarks are relevant, return: {{"relevant_trademark_nbs": []}}


        **Registered Trademarks to Analyze:**
        """

    for i, match in enumerate(formatted_matches, 1):
        prompt += f"""
        **Tradenamrk_nb**: {i}.
        **Trademark Text:** {match['text']}
        **Owner:** {match['applicant_name']}
        **Registration Number:** {match.get('reg_no', 'Not available')}
        **Goods and Services:** {match.get('goods_services', 'Not available')}
        """

    prompt += """**Product Information:**
        - Product Description: {description or "Not provided"}
        - Keywords: {keywords or "Not provided"}
        - Reference Text: {reference_text or "Not provided"}"""

    # Call AI for analysis
    prompt_list = [("text", prompt)]
    if product_image_path and os.path.exists(product_image_path):
        prompt_list.append(("text", "\n- Product Image:"))
        prompt_list.append(("image_path", product_image_path))

    ai_answer = await vertex_genai_multi_async(prompt_list, model_name=Constants.SMART_MODEL_FREE)

    try:
        analysis_result = get_json(ai_answer)
        relevant_indices = analysis_result.get("relevant_trademark_nbs", [])
        # product_url = create_product_url(check_id, product_image_path)

        # Mark relevant matches for report creation
        results = []
        for idx in relevant_indices:
            if 1 <= idx <= len(all_matches):
                match = formatted_matches[idx - 1]
                results.append({
                    "ip_type": "Trademark",
                    "internal_type": "text",
                    "reg_no": match.get("reg_no", []),
                    "int_cls_list": match.get("int_cls_list", []),
                    "goods_services": match.get("goods_services", ""),
                    "text": str(match.get("text", "")),
                    "ip_owner": str(match.get("applicant_name", "")),
                    "plaintiff_id": match.get("plaintiff_id", ""),
                    "ser_no": match.get("ser_no", ""),
                    "product_local_path": product_image_path,
                    # "product_url": [product_url],  # Product url gets added in Create_Report
                    "ip_asset_urls": [create_ip_url("Trademark", match.get("ser_no", ""))],
                    "ip_local_paths": []
                })

        langfuse.get_client().update_current_span(output={"results": results})
        return results

    except Exception as e:
        print(f"Error parsing AI analysis result: {e}")
        langfuse.get_client().update_current_span(output={"results": []})
        return []


@observe(capture_input=False, capture_output=False)
async def process_single_image(client_id, query_image_path, check_id, temp_dir, cases_df, plaintiff_df, split_image=True, embedding_task=None):
    """
    New approach: Extract only brand names from images, then search TM_AUTO and analyze with AI.
    If split_image is False, the whole image is considered a logo.
    """
    langfuse.get_client().update_current_span(input={
        "client_id": client_id, "query_image_path": query_image_path,
        "check_id": check_id, "split_image": split_image
    })
    # prompt_brand = 'Detect all of the Trademarks of generally known brands. For each trademark, assign a label that includes only the brand name/text visible in the trademark. Format: "Brand Name" (one brand name per detection).'
    prompt_brand = 'Detect all of the Trademarks of generally known brands. For each, assign a label that includes the type of trademark (text or logo), and the brand name  in this format: "Type, Brand Name", where Type can only be text or logo. Do not use "trademark" as the label, I need the Type and Brand Name.'

    # For Langfuse
    product_url = create_product_url(check_id, query_image_path)
    image_url = ["", product_url.replace(" ", "%20").replace("http:", "https:")]

    if split_image:
        images_parts = await get_image_parts_async(prompt_brand, query_image_path, image_url, model_name=Constants.IMAGE_MODEL_FREE_LIMITED, useVertexAI=Constants.IMAGE_MODEL_FREE_LIMITED_VERTEX, part_name_infix="_tm") #, model_name=Constants.IMAGE_MODEL_FREE_LIMITED, useVertexAI=Constants.IMAGE_MODEL_FREE_LIMITED_VERTEX)
    else:
        images_parts = [{"path": query_image_path, "label": "logo,unknown"}] # No brand name, it will be searched as a logo

    results = []
    brands = []
    for image_part in images_parts:
        label_parts = image_part["label"].split(",")
        label_type = label_parts[0].lower().strip() if len(label_parts) >= 2 else "unknown"
        brand_name = label_parts[1].strip() if len(label_parts) >= 2 else ""
        if label_type == "logo":
            results.extend(await process_brand_logo(client_id, image_part["path"], product_url, brand_name, check_id, temp_dir, cases_df, plaintiff_df, embedding_task=embedding_task))
        # elif label_type == "text":
        #     brands.extend(brand_name)
        if brand_name and brand_name not in brands and brand_name.lower() != "unknown" and brand_name.lower() != "trademark":
            brands.append(brand_name)

    langfuse.get_client().update_current_span(output={"results": results, "brands": brands})
    return results, brands


@observe(capture_input=False, capture_output=False)
async def process_brand_logo(client_id, query_image_part_path, product_url, brand_name, check_id, temp_dir, cases_df, plaintiff_df, embedding_task=None):
    langfuse.get_client().update_current_span(input={
        "client_id": client_id, "query_image_part_path": query_image_part_path,
        "product_url": product_url, "brand_name": brand_name, "check_id": check_id
    })
    # Logic: if the brand name is one of the TRO plaintiff, we search Qdrant only for that plaintiff. Else we search everything (with higher treshold)
    
    matches, is_perfect = get_matches_and_check_perfect(brand_name)
    match_items = matches if is_perfect else []
    applicant_names = set()
    plaintiff_ids = set()
    for match_item in match_items:
        if match_item[2][4]:
            applicant_names.add(match_item[2][4]) # 2 is metadata, 4 is applicant_name
        if match_item[2][1]:
            plaintiff_ids.add(match_item[2][1]) # 2 is metadata, 1 is plaintiff_id

    sim_results = []
    for plaintiff_id in plaintiff_ids:
        sim_results.extend(await find_similar_assets_qdrant(
            query_image_paths=[query_image_part_path],
            check_id=check_id,
            client_id=client_id,
            ip_type="Trademark",
            temp_dir=temp_dir,
            cases_df=cases_df,
            plaintiff_df=plaintiff_df,
            plaintiff_id=plaintiff_id,
            top_n=1,
            similarity_threshold=0.65,
            embedding_task=embedding_task
        ))
        
    if not match_items or not sim_results:  # If we did not find using the plaintiff id, or we dont have a plaintiff id, we search amongst the logo with a higher threshold.
        # No match in plaintiff_list, so we search for the brand in trademark_texts.
        sim_results = await find_similar_assets_qdrant(
            query_image_paths=[query_image_part_path],
            check_id=check_id,
            client_id=client_id,
            ip_type="Trademark",
            temp_dir=temp_dir,
            cases_df=cases_df,
            plaintiff_df=plaintiff_df,
            plaintiff_id=None,
            top_n=1,
            similarity_threshold=0.75,
            embedding_task=embedding_task
        )
    
    for sim_result in sim_results:
        sim_result["internal_type"] = "logo"

    langfuse.get_client().update_current_span(output={"results": sim_results})
    return sim_results



@observe(capture_input=False, capture_output=False)
# @profile
async def check_trademarks(client, bucket, temp_dir, client_id, check_id, local_product_images, local_client_ip_images, local_reference_images, description, ip_keywords, reference_text, cases_df, plaintiff_df, embedding_task=None, language='zh'):
    langfuse.get_client().update_current_span(input={
        "client_id": client_id, "check_id": check_id,
        "local_product_images": local_product_images,
        "local_client_ip_images": local_client_ip_images,
        "local_reference_images": local_reference_images,
        "description": description, "ip_keywords": ip_keywords, "reference_text": reference_text, "language": language
    })
    start_time = time.time()

    # Use new approach: process images with TM_AUTO search and AI analysis
    trademark_check_tasks = [
        process_single_image(
            client_id, query_image_path, check_id, temp_dir, cases_df, plaintiff_df,
            split_image=(query_image_path not in local_client_ip_images),
            embedding_task=embedding_task
        )
        for query_image_path in local_product_images + local_client_ip_images + local_reference_images
    ]
    task_returns = await asyncio.gather(*trademark_check_tasks)
    results_logo = [item for sublist in task_returns for item in sublist[0] if item is not None]
    brands = [item for sublist in task_returns for item in sublist[1] if item is not None]
    
    
    ### Collect all trademark matches from different source
    # 1. Search for matches of brand names from AI
    text_matches = []
    for brand_name in brands:
        matches, is_perfect = get_matches_and_check_perfect(brand_name)
        if is_perfect:
            text_matches.extend(matches)

    # 2. Search for exact matches of keywords
    for keyword in ip_keywords:
        matches, is_perfect = get_matches_and_check_perfect(keyword)
        if is_perfect:
            text_matches.extend(matches)

    # 3. Search for matches in description text
    text_matches.extend(get_all_matches(description))

    # 4. Search for matches in reference text
    text_matches.extend(get_all_matches(reference_text))

    # Remove duplicates text_matches based on serial number
    unique_matches = []
    seen_ser_nos = set()
    for match in text_matches:
        _, _, metadata = match
        ser_no = metadata[3]  # reg_no is at index 2 in metadata tuple
        if ser_no not in seen_ser_nos:
            seen_ser_nos.add(ser_no)
            unique_matches.append(match)
            
    # unique_matches sort by len("text")
    unique_matches.sort(key=lambda x: len(x[2][0]), reverse=True) # 2 is metadata, 0 is text
    filtered_unique_matches = unique_matches[:15]  # Only keep the 15 longest text (we are not going to give 100 to the AI!)

    # Use AI to analyze which matches are relevant for infringement
    results_text = await analyze_trademark_text_relevance(check_id, local_product_images[0], description, ip_keywords, reference_text, filtered_unique_matches)
    
    results = results_logo + results_text
    
    # Merge results that have same ip_owner and same text:
    merged_results = []
    seen_keys = set()
    for result in results:
        key = (result["ip_owner"], result["text"])
        if key not in seen_keys:
            merged_results.append(result)
            seen_keys.add(key)
        else:
            merged_result = [merged_result for merged_result in merged_results if merged_result["ip_owner"] == result["ip_owner"] and merged_result["text"] == result["text"]][0]
            if result["product_local_path"] == local_product_images[0]:  # Favor the main picture
                merged_result["product_local_path"] = result["product_local_path"]  # Create report adds the product url
                
            merged_result["ip_asset_urls"].extend(result["ip_asset_urls"])
            merged_result["ip_local_paths"].extend(result["ip_local_paths"])
            
    print(f"📋 [CHECK:{check_id}] Logo results: {len(results_logo)}, Text results: {len(results_text)}, Merged results: {len(merged_results)}")
    
    report_tasks = []
    for result in merged_results:
        report_tasks.append(create_check_report("Trademark", check_id, result, client, bucket, description=description, keywords=ip_keywords, reference_text=reference_text, language=language))

    # Upload to COS the images parts (the full image is already uploaded in "check" function) used for these results. We start the upload now, we wait for the result after the "report_tasks" are done
    product_part_paths = set([result["product_local_path"] for result in results if "part" in result["product_local_path"]])
    trademark_product_image_upload_task = [asyncio.create_task(
        async_upload_file_with_retry(client=client, bucket=bucket, key=f"checks/{check_id}/query/{os.path.basename(product_part_path)}", file_path=product_part_path)
    ) for product_part_path in product_part_paths]
    
    report_results = await asyncio.gather(*report_tasks)
    await asyncio.gather(*trademark_product_image_upload_task)
    
    report_results = [res for res in report_results if res is not None]  # Filter out None values (i.e. where the report was not required because not similar at all)

    print(f"\033[32m ✅ Trademark: Trademark Analysis DONE, for {len(local_product_images+local_client_ip_images+local_reference_images)} pictures in {time.time() - start_time:.1f} seconds\033[0m")
    langfuse.get_client().update_current_span(output={"results": report_results})
    return report_results

if __name__ == '__main__':
    # This block allows the script to be run directly for testing purposes.

    import tempfile
    import sys

    # Ensure the project root is in the Python path to resolve imports correctly.
    sys.path.append(os.getcwd())

    from DatabaseManagement.ImportExport import get_table_from_GZ

    async def main():
        """Main function to run a standalone trademark check on a single local image."""
        print("--- Running Standalone Trademark Check ---")
        
        # HARDCODED: Provide a path to a local image for testing.
        test_image_path = r"D:\Win10User\Downloads\Check images\test3\product_1_dd3f3f38-35b6-4ee2-8c3e-2219adcff75b.png"
        local_product_images = [test_image_path]
        
        # Create a persistent directory for output, named after the test file.
        output_dir = os.path.join(os.path.dirname(test_image_path), os.path.splitext(os.path.basename(test_image_path))[0] + "_output")
        os.makedirs(output_dir, exist_ok=True)
        print(f"Using output directory: {output_dir}")

        cases_df = get_table_from_GZ("tb_case", force_refresh=False)
        plaintiff_df = get_table_from_GZ("tb_plaintiff", force_refresh=False)

        print(f"Checking file: {local_product_images[0]}")
        results = await check_trademarks(
            client=None,
            bucket=None,
            temp_dir=output_dir, # Pass the persistent output directory
            client_id="local_test",
            check_id="local_test",
            local_product_images=local_product_images,
            local_client_ip_images=[],
            local_reference_images=[],
            description="",
            ip_keywords=[],
            reference_text="",
            cases_df=cases_df,
            plaintiff_df=plaintiff_df,
            embedding_task=None
        )
        print("\n--- Trademark Check Results ---")
        print(results)
        print("--- End of Report ---")

    asyncio.run(main())