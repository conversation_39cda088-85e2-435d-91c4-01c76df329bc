# Upstream for the API service with failover
upstream api_backend {
    # Primary server (GPU). If it fails 3 times in 30s, it's marked as down.
    server vast.maidalv.com:40324 max_fails=3 fail_timeout=30s;

    # Backup server (local). This is used only when the primary is down.
    # The name "tro_app_api" must be resolvable by the nginx-proxy container.
    # This assumes that tro_app_api is on the same Docker network as nginx-proxy.
    server tro_app_api:5000 backup;
}

# Server block for handling api.maidalv.com requests
server {
    listen 443 ssl;
    http2 on;
    server_name api.maidalv.com;

    # SSL Configuration (paths are inside the nginx-proxy container)
    ssl_certificate /etc/nginx/certs/maidalv.com.crt;
    ssl_certificate_key /etc/nginx/certs/maidalv.com.key;

    # Security enhancements - The nginx-proxy base image handles these.
    # ssl_protocols TLSv1.2 TLSv1.3;
    # ssl_ciphers 'TLS_AES_128_GCM_SHA256:TLS_AES_256_GCM_SHA384:TLS_CHACHA20_POLY1305_SHA256';
    # ssl_prefer_server_ciphers off;

    # Proxy settings
    client_max_body_size 10M;

    location / {
        proxy_pass http://api_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Add a custom header to see which upstream server was used
        add_header X-Upstream-Server $upstream_addr always;

        # This will put the name of the upsetream server in the response (so I can see what server it is going to)
        add_header X-Upstream-Server $upstream_addr;
        
        # WebSocket support
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        
        # Timeouts
        # Timeouts - reduced for faster failover
        proxy_connect_timeout 3s;
        proxy_send_timeout 10s;
        proxy_read_timeout 10s;
    }
}