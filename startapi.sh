#!/bin/bash
### This is the start file for the Docker container

# Save environment variables for SSH sessions
echo "#!/bin/bash" > /etc/profile.d/docker_env.sh
env | grep -v "PATH" | grep -v "PWD" | grep -v "HOME" | grep -v "HOSTNAME" | sed 's/^\([^=]*\)=\(.*\)/export \1="\2"/' >> /etc/profile.d/docker_env.sh
echo 'export PYTHONPATH="/app"' >> /etc/profile.d/docker_env.sh
chmod +x /etc/profile.d/docker_env.sh

# Start Redis server
redis-server --daemonize yes

# Set strict permissions for the private key for SSH sessions
chmod 600 /app/.ssh/id_rsa

# Start SSH server
/usr/sbin/sshd

# Start the Celery worker in the background
# -A: specifies the Celery app instance
# -c: sets the concurrency level (number of parallel worker processes)
# --loglevel: sets the logging level
# --logfile: specifies the log file path
# --detach: runs the worker as a daemon
echo "Starting Celery worker with concurrency 4..."
cd /app && celery -A Check.Celery.celery_app worker -P threads -c 4 --loglevel=info &

# Wait for the Celery worker to be ready
echo "Waiting for Celery worker to initialize..."
cd /app && python Check/Celery/wait_for_celery.py

# Start Flower for Celery monitoring and external access
echo "Starting Flower..."
cd /app && celery -A Check.Celery.celery_app flower --address=0.0.0.0 --port=5089 &

# Start the main application with Gunicorn using --preload
# The --preload flag loads the application code in the master process before forking worker processes.
# This allows for shared memory of large objects like ML models, saving significant RAM.
echo "Starting Gunicorn with --preload..."
# We keep gunicorn running in the foreground to keep the container alive.
# cd /app && gunicorn --workers 4 --bind 0.0.0.0:5000 --preload app_apistudio:standalone_app --certfile=/ssl/live/maidalv.com/fullchain.pem --keyfile=/ssl/live/maidalv.com/privkey.pem
cd /app && gunicorn --workers 4 --bind 0.0.0.0:5000 --preload app_apistudio:standalone_app