#!/usr/bin/env python3
"""
Script to delete specific cases from the tb_case table by case IDs.
This also deletes related case steps to maintain database integrity.
"""

import sys
import os
sys.path.append(os.getcwd())

from DatabaseManagement.Connections import get_gz_connection
from DatabaseManagement.ImportExport import get_table_from_GZ


def delete_cases_by_ids(case_ids):
    """
    Delete cases from tb_case table by a list of case IDs.
    Also deletes related case steps to maintain database integrity.

    Args:
        case_ids (list[int]): List of case IDs to delete

    Returns:
        dict: Summary of deletions
    """
    if not case_ids:
        print("No case IDs provided.")
        return {"deleted": 0, "steps_deleted": 0, "skipped": 0}

    connection = get_gz_connection()
    cursor = connection.cursor()

    deleted_cases = 0
    deleted_steps = 0
    skipped = 0

    try:
        for case_id in case_ids:
            # Get case info
            cursor.execute("SELECT id, docket, date_filed, court FROM tb_case WHERE id = %s", (case_id,))
            case_data = cursor.fetchone()

            if not case_data:
                print(f"Case ID {case_id} not found. Skipping.")
                skipped += 1
                continue

            case_id, docket, date_filed, court = case_data
            print(f"\nFound case ID {case_id}: Docket={docket}, Filed={date_filed}, Court={court}")

            # Check related steps
            cursor.execute("SELECT COUNT(*) FROM tb_case_steps WHERE case_id = %s", (case_id,))
            steps_count = cursor.fetchone()[0]

            # Delete steps
            if steps_count > 0:
                cursor.execute("DELETE FROM tb_case_steps WHERE case_id = %s", (case_id,))
                steps_deleted = cursor.rowcount
                deleted_steps += steps_deleted
                print(f"Deleted {steps_deleted} steps for case ID {case_id}")

            # Delete case
            cursor.execute("DELETE FROM tb_case WHERE id = %s", (case_id,))
            case_deleted = cursor.rowcount
            deleted_cases += case_deleted
            print(f"Deleted case ID {case_id} (Docket: {docket})")

        connection.commit()
        print(f"\n✓ Deletion Summary: {deleted_cases} case(s), {deleted_steps} step(s) deleted. {skipped} case(s) skipped.")
        return {"deleted": deleted_cases, "steps_deleted": deleted_steps, "skipped": skipped}

    except Exception as e:
        connection.rollback()
        print(f"❌ Error during deletion: {e}")
        raise e
    finally:
        cursor.close()
        connection.close()


def verify_cases_deleted(case_ids):
    """
    Verify that the given case IDs have been deleted.

    Args:
        case_ids (list[int]): Case IDs to verify
    """
    print("\nVerifying deletions...")
    cases_df = get_table_from_GZ("tb_case", force_refresh=True)
    remaining = cases_df[cases_df['id'].isin(case_ids)]

    if remaining.empty:
        print("✓ All specified cases have been successfully deleted.")
    else:
        print("✗ Some cases still exist:")
        print(remaining[['id', 'docket', 'date_filed', 'court']])


def find_duplicate_case_ids():
    """
    Find duplicate cases based on docket and court.
    Returns list of duplicate case IDs to delete (keeping the earliest one per group).
    """
    print("🔍 Loading cases from database...")
    cases_df = get_table_from_GZ("tb_case", force_refresh=True)

    # Drop cases with missing docket or court
    cases_df = cases_df.dropna(subset=['docket', 'court'])

    # Sort so the "last" per group is most recent
    cases_df = cases_df.sort_values(by=['docket', 'court', 'date_filed', 'id'], ascending=[True, True, False, True])

    # Mark duplicates (keeping the first in each group)
    cases_df['is_duplicate'] = cases_df.duplicated(subset=['docket', 'court'], keep='last')

    # Get IDs of the duplicates
    duplicate_ids = cases_df[cases_df['is_duplicate']]['id'].tolist()

    print(f"Found {len(duplicate_ids)} duplicate case(s) to delete.")
    return duplicate_ids

    
if __name__ == "__main__":
    # List of case IDs to delete
    # ids_to_delete = [14612, 14613, 14614, 14615, 14611]
    ids_to_delete = find_duplicate_case_ids()

    print(f"Starting deletion process for case IDs: {ids_to_delete}")
    print("=" * 60)

    if ids_to_delete:
        result = delete_cases_by_ids(ids_to_delete)
        print("=" * 60)
        verify_cases_deleted(ids_to_delete)
    else:
        print("✓ No duplicates found. Nothing to delete.")