import { initializeEventListeners, updateFilters } from './modules/eventHandlers.js';
import { fetchCases } from './modules/api.js';
import { setFilters } from './modules/state.js';
import { caseNumberInput, plaintiffIdInput } from './modules/dom.js';

document.addEventListener('DOMContentLoaded', function() {
    function applyUrlParametersAndFetch() {
        const urlParams = new URLSearchParams(window.location.search);
        const urlCaseId = urlParams.get('case_id');
        const urlPlaintiffId = urlParams.get('plaintiff_id');

        let filtersAppliedFromUrl = false;

        if (urlCaseId) {
            caseNumberInput.value = urlCaseId;
            filtersAppliedFromUrl = true;
        }
        if (urlPlaintiffId) {
            plaintiffIdInput.value = urlPlaintiffId;
            filtersAppliedFromUrl = true;
        }

        if (filtersAppliedFromUrl) {
            setFilters({ offset: 0 });
            updateFilters();
            fetchCases();
        } else {
            fetchCases(false);
        }
    }

    initializeEventListeners();
    applyUrlParametersAndFetch();
});
