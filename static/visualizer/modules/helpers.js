import {
    fetchCaseSteps
} from './api.js';

export function addInfoItem(container, label, value) {
    const item = document.createElement('div');
    item.className = 'info-item';
    const labelEl = document.createElement('span');
    labelEl.className = 'info-label';
    labelEl.textContent = label;
    const valueEl = document.createElement('span');
    valueEl.className = 'info-value';
    valueEl.textContent = value;
    item.appendChild(labelEl);
    item.appendChild(valueEl);
    container.appendChild(item);
    return item;
}

export function getImagePath(plaintiffId, imageName, ipType) {
    const baseUrl = 'http://troimages-1329604052.cos.ap-guangzhou.myqcloud.com/plaintiff_images/';
    return `${baseUrl}${plaintiffId}/low/${imageName}`;
}

export function addIpInfoItem(container, label, value) {
    const item = document.createElement('div');
    item.className = 'ip-info-item';
    const labelEl = document.createElement('span');
    labelEl.className = 'ip-info-label';
    labelEl.textContent = label;
    const valueEl = document.createElement('span');
    valueEl.className = 'ip-info-value';
    valueEl.textContent = value;
    item.appendChild(labelEl);
    item.appendChild(valueEl);
    container.appendChild(item);
    return item;
}

export function makeInfoItemValueLinkable(infoItem, plaintiffId, label, regNo, fullFilename) {
    if (!infoItem || !regNo || !fullFilename) return;

    const valueEl = infoItem.querySelector('.ip-info-value');
    if (!valueEl) return;

    const regNoArray = Array.isArray(regNo) ? regNo : [regNo];
    const filenameArray = Array.isArray(fullFilename) ? fullFilename : [fullFilename];

    if (regNoArray.length === 1 && filenameArray.length === 1) {
        const link = document.createElement('a');
        const highQualityUrl = `http://troimages-1329604052.cos.ap-guangzhou.myqcloud.com/plaintiff_images/${plaintiffId}/high/${filenameArray[0]}`;
        link.href = highQualityUrl;
        link.textContent = regNoArray[0];
        link.style.cursor = 'pointer';
        link.addEventListener('click', (e) => {
            e.preventDefault();
            window.Fancybox.show([{
                src: highQualityUrl,
                caption: `${regNoArray[0]}<br>${filenameArray[0]}`
            }]);
        });
        valueEl.textContent = '';
        valueEl.appendChild(link);
    }
}

export function createMultiSiteSearchLinks(container, docketFormat) {
    const wwwLink = document.createElement('a');
    wwwLink.textContent = 'WWW';
    wwwLink.className = 'action-link';
    wwwLink.title = 'Search across multiple websites';

    if (docketFormat) {
        wwwLink.href = '#';
        wwwLink.onclick = function(e) {
            e.preventDefault();
            window.open(`https://www.10100.com/search/${encodeURIComponent(docketFormat)}`, '_blank');
            window.open(`https://sellerdefense.cn/?s=${encodeURIComponent(docketFormat)}`, '_blank');
            window.open(`https://maijiazhichi.com/?s=${encodeURIComponent(docketFormat)}`, '_blank');
        };
    } else {
        wwwLink.classList.add('disabled');
        wwwLink.title = 'Cannot generate search links (invalid docket format)';
        wwwLink.onclick = (e) => e.preventDefault();
    }
    container.appendChild(wwwLink);
}

export function addStepsInfoItem(container, caseId) {
    const item = document.createElement('div');
    item.className = 'info-item steps-info-item';
    item.setAttribute('data-case-id', caseId);

    const labelEl = document.createElement('span');
    labelEl.className = 'info-label';
    labelEl.textContent = '# Steps:';

    const valueEl = document.createElement('span');
    valueEl.className = 'steps-count';
    valueEl.textContent = '?';

    const loadingEl = document.createElement('span');
    loadingEl.className = 'steps-loading';
    loadingEl.style.display = 'none';
    loadingEl.innerHTML = ' <i class="fas fa-spinner fa-spin"></i>';

    item.appendChild(labelEl);
    item.appendChild(valueEl);
    item.appendChild(loadingEl);

    item.addEventListener('click', function() {
        const caseId = this.getAttribute('data-case-id');
        const stepsSection = document.getElementById(`steps-section-${caseId}`);
        if (stepsSection) {
            stepsSection.style.display = stepsSection.style.display === 'none' ? 'block' : 'none';
        } else {
            fetchCaseSteps(caseId);
        }
    });

    container.appendChild(item);
    return item;
}
export function createOrGetLogSection(caseCard, titleText, caseIdForTitle) {
    let progressLog = caseCard.querySelector('.log-section');
    let logContentElement;
    let logTitleElement;

    if (progressLog) {
        logContentElement = progressLog.querySelector('.log-section-content');
        logTitleElement = progressLog.querySelector('.log-section-title');
        if (logTitleElement) logTitleElement.textContent = `${titleText} for Case ${caseIdForTitle}`;
        if (logContentElement) logContentElement.textContent = '';
        progressLog.style.display = 'block';
    } else {
        progressLog = document.createElement('div');
        progressLog.className = 'log-section';
        const logHeader = document.createElement('div');
        logHeader.className = 'log-section-header';
        logTitleElement = document.createElement('div');
        logTitleElement.className = 'log-section-title';
        logTitleElement.textContent = `${titleText} for Case ${caseIdForTitle}`;
        const closeLogBtn = document.createElement('button');
        closeLogBtn.className = 'close-log';
        closeLogBtn.innerHTML = '&times;';
        closeLogBtn.addEventListener('click', () => progressLog.style.display = 'none');
        logHeader.appendChild(logTitleElement);
        logHeader.appendChild(closeLogBtn);
        logContentElement = document.createElement('div');
        logContentElement.className = 'log-section-content';
        progressLog.appendChild(logHeader);
        progressLog.appendChild(logContentElement);
        const detailsRow = caseCard.querySelector('.case-details-row');
        if (detailsRow && detailsRow.nextSibling) {
            caseCard.insertBefore(progressLog, detailsRow.nextSibling);
        } else {
            caseCard.appendChild(progressLog);
        }
    }
    return { progressLog, logContentElement, logTitleElement };
}