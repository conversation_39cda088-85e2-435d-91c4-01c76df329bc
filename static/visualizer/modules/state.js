// State
export let currentPage = 1;
export let totalPages = 1;
export let currentData = [];
export let currentFilters = {
    case_number: '',
    plaintiff_name: '',
    plaintiff_id: '',
    case_type: [],
    picture_type: '',
    validation_status: '',
    ip_source: '',
    date_from: '',
    date_to: '',
    sort_by: 'date_filed',
    sort_order: 'desc',
    limit: 20,
    offset: 0
};
export let proposedPlaintiffNames = {};

export function setCurrentPage(page) {
    currentPage = page;
}

export function setTotalPages(pages) {
    totalPages = pages;
}

export function setCurrentData(data) {
    currentData = data;
}

export function setFilters(filters) {
    currentFilters = { ...currentFilters, ...filters };
}

export function resetFilters() {
    currentFilters.offset = 0;
    currentPage = 1;
}

export function setProposedPlaintiffNames(names) {
    proposedPlaintiffNames = names;
}

export function updateCurrentData(caseId, updatedCase) {
    const index = currentData.findIndex(c => c.id === caseId);
    if (index !== -1) {
        currentData[index] = updatedCase;
    }
}

export function updateValidationStatusInState(caseId, status) {
    const caseItem = currentData.find(c => c.id === caseId);
    if (caseItem) {
        caseItem.validation_status = status;
    }
}