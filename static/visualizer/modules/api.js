import {
    currentFilters,
    setProposedPlaintiffNames,
    setCurrentData,
    setTotalPages,
    updateCurrentData
} from './state.js';
import {
    renderCases,
    updatePagination,
    showLoading,
    showError,
    updateCaseCard,
    updateValidationIcons,
    displayCaseSteps,
    updateStepsCount
} from './ui.js';
import { casesContainer } from './dom.js';
import { createOrGetLogSection } from './helpers.js';

export function fetchCases(refreshAll = false, refreshSelection = false) {
    showLoading();

    const params = new URLSearchParams();

    for (const [key, value] of Object.entries(currentFilters)) {
        if (value !== '' && !Array.isArray(value)) {
            params.append(key, value);
        }
    }

    if (currentFilters.case_type.length > 0) {
        currentFilters.case_type.forEach(type => {
            params.append('case_type[]', type);
        });
    }

    if (refreshAll) {
        params.append('refresh_all', 'true');
    } else if (refreshSelection) {
        params.append('refresh_selection', 'true');
        for (const [key, value] of Object.entries(currentFilters)) {
            if (value !== '' && !Array.isArray(value)) {
                params.append(key, value);
            } else if (Array.isArray(value) && value.length > 0) {
                value.forEach(item => params.append(`${key}[]`, item));
            }
        }
    }

    fetch(`/api/cases?${params.toString()}`)
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(data => {
            setCurrentData(data.data);
            setTotalPages(Math.ceil(data.total / currentFilters.limit));

            if (data.proposed_names) {
                setProposedPlaintiffNames(data.proposed_names);
                const event = new CustomEvent('casesLoaded', {
                    detail: {
                        proposed_names: data.proposed_names
                    }
                });
                casesContainer.dispatchEvent(event);
            }

            renderCases(data.data);
            updatePagination(data.total);
        })
        .catch(error => {
            showError(`Error loading cases: ${error.message}`);
        });
}

export function updateCaseWithOptions(caseId, processingOptions, logContent, logTitle) {
    const requestData = {
        case_id: caseId,
        processing_options: processingOptions
    };

    fetch('/api/cases/reprocess', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData),
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('Network response was not ok');
        }
        return response.json();
    })
    .then(data => {
        if (data.queued) {
            logContent.textContent += 'Case is already queued for reprocessing.\n';
        } else if (data.queue_position > 1) {
            logContent.textContent += `Case added to queue. Position: ${data.queue_position}\n`;
            logContent.textContent += 'The case will be processed when previous cases are complete.\n';
        } else {
            logContent.textContent += 'Reprocess initiated successfully. The case will be reprocessed in the background.\n';
        }

        const eventSource = new EventSource(`/api/cases/reprocess/status/${caseId}`);

        eventSource.onmessage = function(event) {
            const message = event.data;
            if (message.includes('Queue position:') || message.includes('queue position:') ||
                message.includes('Case added to queue') || message.includes('Starting next case in queue')) {
                logTitle.textContent = 'Reprocess Progress - ' + message.split('(')[1]?.split(')')[0] || 'Queued';
            }
            logContent.textContent += message + '\n';
            logContent.scrollTop = logContent.scrollHeight;
        };

        eventSource.onerror = function() {
            eventSource.close();
            const lastMessage = logContent.textContent.trim().split('\n').pop();
            const casesRemaining = lastMessage.includes('remaining') ||
                                   lastMessage.includes('Starting next case in queue') ||
                                   lastMessage.includes('Processing complete') && lastMessage.includes('cases remaining');

            logContent.textContent += 'Reprocess complete for this case.\n';

            if (casesRemaining) {
                logContent.textContent += 'Continuing with next case in queue...\n';
            } else {
                logContent.textContent += 'Reprocess complete.\n';
            }

            const params = new URLSearchParams();
            params.append('case_id', caseId);

            fetch(`/api/cases?${params.toString()}`, { cache: 'no-cache' })
                .then(response => response.json())
                .then(responseData => {
                    if (responseData.data && responseData.data.length > 0) {
                        const updatedCase = responseData.data[0];
                        if (updatedCase.id === caseId) {
                            if (responseData.proposed_names) {
                                Object.assign(proposedPlaintiffNames, responseData.proposed_names);
                            }
                            updateCurrentData(caseId, updatedCase);
                            updateCaseCard(caseId, updatedCase);
                        } else {
                            logContent.textContent += `Warning: Fetched data for wrong case ID (${updatedCase.id}).\n`;
                        }
                    } else {
                         logContent.textContent += `Warning: No updated data found for case ${caseId}.\n`;
                    }
                })
                .catch(error => {
                    logContent.textContent += `Error refreshing case data: ${error.message}\n`;
                });
        };
    })
    .catch(error => {
        logContent.textContent += `Error: ${error.message}\n`;
    });
}

export function updateValidationStatus(caseId, status) {
    fetch('/api/cases/validation', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            case_id: caseId,
            validation_status: status
        }),
    })
    .then(response => {
        if (!response.ok) {
            return response.json().then(data => {
                throw new Error(data.error || 'Failed to update validation status');
            });
        }
        return response.json();
    })
    .then(data => {
        if (data.success) {
            updateValidationIcons(caseId, status);
        } else {
            throw new Error(data.error || 'Failed to update validation status');
        }
    })
    .catch(error => {
        console.error('Error updating validation status:', error);
        alert('Failed to update validation status: ' + error.message);
        location.reload();
    });
}

export function improvePlaintiffName(button) {
    const caseId = button.dataset.caseId;
    if (!caseId) return;

    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
    button.disabled = true;

    fetch('/api/cases/improve-plaintiff', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ case_id: caseId })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            button.innerHTML = '<i class="fas fa-check"></i>';
            button.className = 'improve-plaintiff-btn success';
            button.title = 'Plaintiff name improved';

            if (data.proposed_name) {
                setProposedPlaintiffNames({ ...proposedPlaintiffNames, [caseId]: data.proposed_name });
                const caseCard = button.closest('.case-card');
                const caseTitleElement = caseCard.querySelector('.case-title');
                if (caseTitleElement) {
                    const currentName = caseTitleElement.textContent.split('(New proposed name:')[0].trim();
                    caseTitleElement.innerHTML = `${currentName} <span class="proposed-name"><a href="/plaintiff_review">(New proposed name: ${data.proposed_name})</a></span>`;
                }
            }
        } else {
            button.innerHTML = '<i class="fas fa-exclamation-triangle"></i>';
            button.className = 'improve-plaintiff-btn error';
            button.title = 'Error: ' + (data.error || 'Failed to improve name');

            setTimeout(() => {
                button.innerHTML = '<i class="fas fa-wand-magic-sparkles"></i>';
                button.className = 'improve-plaintiff-btn';
                button.title = 'Improve Plaintiff Name';
                button.disabled = false;
            }, 3000);
        }
    })
    .catch(error => {
        console.error('Error improving plaintiff name:', error);
        button.innerHTML = '<i class="fas fa-exclamation-triangle"></i>';
        button.className = 'improve-plaintiff-btn error';
        button.title = 'Error: ' + error.message;

        setTimeout(() => {
            button.innerHTML = '<i class="fas fa-wand-magic-sparkles"></i>';
            button.className = 'improve-plaintiff-btn';
            button.title = 'Improve Plaintiff Name';
            button.disabled = false;
        }, 3000);
    });
}

export function fetchCaseSteps(caseId) {
    const stepsInfoItem = document.querySelector(`.steps-info-item[data-case-id="${caseId}"]`);
    if (stepsInfoItem) {
        stepsInfoItem.querySelector('.steps-loading').style.display = 'inline';
    }

    fetch(`/api/cases/${caseId}/steps`)
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(data => {
            const stepNumbersCount = {};
            let hasDuplicates = false;

            data.steps.forEach(step => {
                if (step.step_nb !== null) {
                    const stepNumber = Number(step.step_nb);
                    const stepKey = isNaN(stepNumber) ? step.step_nb : stepNumber.toString();
                    stepNumbersCount[stepKey] = (stepNumbersCount[stepKey] || 0) + 1;
                    if (stepNumbersCount[stepKey] > 1) {
                        hasDuplicates = true;
                    }
                }
            });

            if (stepsInfoItem) {
                updateStepsCount(stepsInfoItem, data.steps.length, hasDuplicates);
            }

            const caseCard = stepsInfoItem.closest('.case-card');
            if (caseCard) {
                displayCaseSteps(caseCard, caseId, data.steps, stepNumbersCount);
            }
        })
        .catch(error => {
            console.error('Error fetching steps:', error);
            if (stepsInfoItem) {
                stepsInfoItem.querySelector('.steps-loading').style.display = 'none';
                stepsInfoItem.querySelector('.steps-count').textContent = 'Error';
            }
        });
}

export function fetchHistoricalLogs(caseId, caseCardElement) {
    const { logContentElement } = createOrGetLogSection(caseCardElement, 'Historical Logs', caseId);
    logContentElement.textContent = 'Loading historical logs...\n';
    logContentElement.scrollTop = logContentElement.scrollHeight;

    fetch(`/api/cases/${caseId}/logs`)
        .then(response => response.json())
        .then(data => {
            logContentElement.textContent = '';
            if (data.success && data.logs) {
                if (data.logs.length > 0) {
                    data.logs.forEach(logEntry => {
                        const timestamp = new Date(logEntry.timestamp).toLocaleString();
                        logContentElement.textContent += `[${timestamp}] [${logEntry.level}] ${logEntry.message}\n`;
                    });
                } else {
                    logContentElement.textContent = 'No historical logs found for this case.\n';
                }
            } else {
                logContentElement.textContent = `Error loading logs: ${data.error || 'Unknown error'}\n`;
            }
            logContentElement.scrollTop = logContentElement.scrollHeight;
        })
        .catch(error => {
            console.error('Error fetching historical logs:', error);
            logContentElement.textContent += `Failed to fetch logs: ${error.message}\n`;
            logContentElement.scrollTop = logContentElement.scrollHeight;
        });
}