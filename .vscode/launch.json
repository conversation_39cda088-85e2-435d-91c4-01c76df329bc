{"version": "0.2.0", "configurations": [{"name": "Python: Current File", "type": "debugpy", "request": "launch", "program": "${file}", "console": "integratedTerminal"}, {"name": "Python: Flask (Debug Celery Eager Mode)", "type": "debugpy", "request": "launch", "module": "flask", "env": {"FLASK_APP": "app_apistudio.py", "FLASK_ENV": "development", "CELERY_ALWAYS_EAGER": "True"}, "args": ["run", "--no-reload"], "jinja": true}, {"name": "PowerShell: Current File", "type": "PowerShell", "request": "launch", "script": "${file}", "cwd": "${fileDirname}"}]}